.assign-item-container {
  cursor: pointer;
  padding: 16px 16px 15px 16px;
  border-bottom: 1px solid #CDC8E5;
  display: flex;
  flex-direction: column;
  gap: 8px;

  &:hover {
    background-color: #F8F7FF;
    border-bottom-color: #3A18CE;;
  }

  .assign-item__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .assign-item__time {
      font-size: 13px;
      line-height: 20px;
      color: #617087;
    }

    .assign-item__action {
      .ant-btn .ant-btn-icon svg {
        width: 20px;
        height: 20px;
      }

      .ant-dropdown .ant-dropdown-menu.ant-dropdown-menu-root {
        padding: 0;
        gap: 0;

        .ant-dropdown-menu-title-content{
          display: flex;
          white-space: nowrap;
        }
      }
    }

  }

  .assign-item__body {
    font-weight: 500;
    color: var(--navy);
  }

  .assign-item__footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .assign-item__tags {
    }

    .assign-item__score {
      color: #4B2CC9;
      font-size: 13px;
      font-weight: 700;
      line-height: 20px;
    }
  }
}
