.shadowing-screen {
  padding: 32px;
  border-radius: 24px;
  background: radial-gradient(18.71% 33.59% at 50% 8.03%, #f4f3ff 0.01%, #ffffff 100%);
  box-shadow: 0 4px 20px 0 #0000001a;
  display: flex;
  background-color: #f5f5f5;
  flex-direction: column;
  gap: 1rem;

  &__container {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 832px;
    margin: 0 auto;
    width: 100%;
    gap: 16px;
  }

  &__topic-info {
    margin-bottom: 24px;
    text-align: center;

    h1 {
      color: var(--primary-colours-blue-navy);
      line-height: 40px;
      margin: 0;

      font-family: 'Inter';
      font-style: normal;
      font-weight: 600;
      font-size: 22px;
      line-height: 30px;
    }
  }

  &__mode-select {
    width: 50%;
    height: 48px;
    display: flex;
    gap: 8px;
    align-items: center;

    span {
      color: var(--variable-collection-clickee-text-color-body);
      font-family: var(--clickee-title-medium-font-family);
      font-size: var(--clickee-title-medium-font-size);
      font-style: var(--clickee-title-medium-font-style);
      font-weight: var(--clickee-title-medium-font-weight);
      letter-spacing: var(--clickee-title-medium-letter-spacing);
      line-height: var(--clickee-title-medium-line-height);
    }

    .ant-select-selector {
      padding: 12px 16px;
      border-radius: 16px;
      border: 1px solid #dbdbdb;

      .ant-select-selection-item {
        font-size: 16px;
        font-weight: 500;
        color: var(--primary-colours-blue-navy);
        line-height: 20px;
      }
    }

    .ant-select-arrow {
      .ant-select-suffix {
        svg {
          fill: var(--primary-colours-blue-navy);
        }
      }
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    justify-content: space-between;

    .reset-button {
      width: 68px;
      height: 24px;
      padding: 0px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      gap: 8px;
      position: relative;
      border: none;
      border-radius: 8px;

      img {
        width: 16px;
        height: 16px;
      }

      span {
        font-size: 16px;
        font-family: Inter;
        color: #26d06d;
        line-height: 24px;
        text-align: left;
        vertical-align: text-top;
        display: flex;
        border-style: hidden;
        outline: none;
      }

      &:hover {
        background: #f8f8ff !important;
      }

      &.disabled,
      &[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
        background: transparent !important;

        &:hover {
          background: transparent !important;
        }
      }
    }

    .navigation-group {
      display: flex;
      align-items: center;
      gap: 4px;

      .nav-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        padding: 0;
        border: none;
        background: transparent;
        transition: all 0.3s ease;

        &:hover:not(.disabled) {
          background: rgba(58, 24, 206, 0.1);
        }

        &:active:not(.disabled) {
          background: rgba(58, 24, 206, 0.2);
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        img {
          width: 32px;
          height: 32px;
        }

        &.disabled img {
          filter: invert(19%) sepia(97%) saturate(3836%) hue-rotate(246deg) brightness(88%) contrast(98%) opacity(50%);
        }
      }
    }
  }

  &__progress {
    font-size: 22px;
    font-weight: 500;
    font-family: Inter;
    color: #000000;
    line-height: 30px;
    text-align: left;
    vertical-align: text-top;
    border-style: hidden;
    outline: none;
  }

  &__audio-player {
    border-radius: 16px;
    height: 48px;
    background-color: #f6f8fa;

    .audio-player {
      height: 48px;
    }

    .audio-controls {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .audio-progress {
      flex: 1;
      height: 4px;
      background-color: #e0e0e0;
      border-radius: 2px;
      position: relative;
    }

    .audio-time {
      position: absolute;
      right: 0;
      top: -20px;
      font-size: 0.9rem;
      color: #666;
    }
  }

  &__exercise {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .exercise-text {
      &__content {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: #ffffff;
        border: 1px solid #dbdbdb;
        border-radius: 16px;

        span {
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          font-size: 16px;
          line-height: 1.5;
          color: #000000;
          flex: 1;
          text-align: center;
        }

        .toggle-text-icon {
          width: 16px;
          height: 16px;
          cursor: pointer;
          color: #747ca4;

          &:hover {
            opacity: 0.8;
          }
        }
      }
    }

    .exercise-hint {
      font-size: 14px;
      line-height: 20px;
      color: #4e5472;
      margin-bottom: 16px;
      padding: 12px 16px;
      background: #f8f8ff;
      border-radius: 8px;
    }

    .exercise-input {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding: 24px 16px;
      gap: 8px;
      height: 72px;
      background: #ffffff;
      border: 1px solid #dbdbdb;
      border-radius: 16px;

      .input-field {
        height: 24px;
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        text-align: center;
        color: #000000;
        border: none;

        &::placeholder {
          color: #b3b3b3;
        }

        &:focus {
          outline: none;
        }

        &.correct {
          color: #26d06d;
        }

        &.incorrect {
          color: #ff0307;
        }
      }
    }
  }

  &__speaking-recorder {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 24px;
    background: #ffffff;
    border: 1px solid #dbdbdb;
    border-radius: 16px;
    width: 100%;

    .speaking-record__recorder {
      padding: 0;
    }
  }

  &__speaking-result {
    padding: 24px;
    background: #ffffff;
    border: 1px solid #dbdbdb;
    border-radius: 16px;
    width: 100%;
    justify-content: center;
    align-items: center;

    .speaking-result {
      margin: 0;
      padding: 0;
      align-items: center;
      display: flex;
      align-items: center;
    }

    .speaking-result__placeholder {
      line-height: 25px;
      color: #B3B3B3;
      text-align: center;
      width: 100%;
    }

    .result-content {
      display: flex;
      flex-direction: column;
      width: 100%;
      align-items: center;
      text-align: center;
      gap: 8px;

      .result-text {
        font-family: 'Inter';
        font-weight: 400;
        font-size: 16px;
        line-height: 1.5em;
        color: #000000;
        width: 100%;
      }

      .result-score {
        font-family: 'Inter';
        font-weight: 700;
        font-size: 16px;
        line-height: 1.5em;
        color: #000000;
      }
    }
  }

  &__speaking-audio-recorded {
    grid-column: span 1 / span 1;

    .speaking-section {
      display: flex;
      flex-direction: column;
      padding: 15px;
      gap: 8px;
      border-radius: 16px;
      border: 1px solid #dbdbdb;
      background-color: var(--white);

      .speaking-section__title {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .speaking-section__title-text {
          color: var(--navy);
          display: flex;
          align-items: center;
          font-weight: 500;
        }

        .speaking-section__title-extra {
          display: inline-block;
          font-size: 12px;
          line-height: 16px;
          padding: 4px 8px;
          border-radius: 8px;
          color: #318d62;
          background-color: #e5fff3;
        }

        .speaking-section__title-action {
          height: 20px;
          display: flex;
          align-items: center;
          margin-left: auto;

          .ant-btn {
            gap: 4px;
            border-radius: 8px;
            font-size: 12px;
          }
        }
      }

      .speaking-section__divider {
        height: 1px;
        background-color: #dbdbdb;
      }

      .speaking-record {
        display: flex;
        gap: 8px;

        &.speaking-record__centered {
          align-self: center;
        }
      }

      .speaking-topic__tag {
        display: flex;
        gap: 2px;
        font-size: 12px;
        line-height: 20px;
        align-items: center;

        .speaking-topic__tag-label {
          font-weight: 500;
          color: var(--primary-colours-blue-navy);

          &::after {
            content: ':';
          }
        }
      }
    }
  }

  &__speaking-recorder-text {
    font-family: 'Inter';
    font-weight: 500;
    font-size: 16px;
    line-height: 1.5em;
    color: #000000;
    text-align: center;
    margin-top: 16px;
  }

  .exercise-correct-answer {
    background: #f6f8fa;
    border-radius: 16px;
    padding: 16px;
    width: 100%;
    gap: 8px;

    .answer-label {
      display: flex;
      align-items: center;
      gap: 9px;

      img {
        width: 24px;
        height: 24px;
      }

      span {
        font-family: 'Inter';
        font-weight: 500;
        font-size: 16px;
        line-height: 1.5em;
      }

      &.correct {
        span {
          color: #26d06d;
        }
      }

      &.incorrect {
        span {
          color: #ff0307;
        }
      }
    }

    .answer-content {
      display: flex;
      align-items: center;
      gap: 8px;

      img.toggle-answer-icon {
        width: 16px;
        height: 16px;

        &.clickable {
          cursor: pointer;
          transition: opacity 0.3s ease;

          &:hover {
            opacity: 0.7;
          }
        }
      }

      span {
        color: #000000;
        font-family: 'Inter';
        font-weight: 500;
        font-size: 16px;
        line-height: 1.5em;
        user-select: none;
      }
    }
  }

  &__actions {
    display: flex;
    justify-content: center;
    gap: 8px;

    .check-button,
    .next-button {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      border-radius: 12px;
      font-family: 'Inter';
      font-size: 16px;
      font-weight: 600;
      line-height: 1.5em;
      padding: 8px 24px;
      min-width: 120px;
      transition: all 0.3s ease;
    }

    .check-button {
      background: #09196b;
      border: none;
      color: #ffffff;

      &:hover {
        background: darken(#09196b, 5%);
      }

      &:disabled {
        background: #e7e5ff;
        color: #a3a3a3;
        cursor: not-allowed;
      }
    }

    .next-button {
      background: #26d06d;
      border: none;
      color: #ffffff;

      &:hover {
        background: darken(#26d06d, 5%) !important;
        color: #ffffff !important;
      }

      &:disabled {
        background: #f8f8ff;
        border: 1px solid #e7e5ff;
        color: #a3a3a3;
        cursor: not-allowed;
      }
    }
  }

  &__score-card {
    display: flex;
    justify-content: center;

    .score-card__content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 0px;
      width: 320px;
      height: 131px;
      background: #ffffff;
      box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
      border-radius: 24px;
      flex: none;
      order: 0;
      flex-grow: 0;

      .score-card__title {
        color: #000000;
        font-family: 'Inter';
        font-size: 16px;
        font-weight: 500;
        line-height: 1.3em;
        text-align: center;
        margin-bottom: 0;
      }

      .score-card__count {
        display: flex;
        justify-content: center;
        align-items: baseline;
        margin-bottom: 0;

        .correct-count {
          color: #09196b;
          font-family: 'Inter';
          font-size: 60px;
          font-weight: 600;
          line-height: 1.3em;
          text-align: center;
        }

        .total-count {
          color: #09196b;
          font-family: 'Inter';
          font-size: 60px;
          font-weight: 600;
          line-height: 1.3em;
          text-align: center;
        }
      }
    }
  }

  &__transcript {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
    gap: 16px;
    background: #ffffff;
    border: 1px solid #dbdbdb;
    border-radius: 16px;

    .transcript-toggle {
      display: flex;
      align-items: center;
      gap: 8px;

      .custom-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        user-select: none;

        img {
          width: 24px;
          height: 24px;
        }

        &:hover {
          img {
            opacity: 0.8;
          }
        }

        span {
          font-family: 'Inter';
          font-style: normal;
          font-weight: 500;
          font-size: 16px;
          color: #09196b;
        }
      }
    }

    .transcript-content {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      padding: 16px;
      gap: 8px;
      height: 424px;
      background: #e7e5ff;
      border: 1px dashed #dbdbdb;
      border-radius: 16px;
      overflow: hidden;
      width: 100%;

      .transcript-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 4px 0px;
        gap: 10px;
        width: 736px;
        height: 56px;
        border-radius: 8px;
        flex: none;
        align-self: stretch;
        flex-grow: 0;
        margin-bottom: 8px;

        p {
          margin: 0;
        }

        &.active {
          background: #d6d2ff;
        }
      }

      .transcript-divider {
        width: 100%;
        height: 1px;

        border-bottom: 1px dashed #3a18ce;
        margin-bottom: 8px;

        flex: none;
        flex-grow: 0;
      }

      &__audio-player {
        border-radius: 16px;
        height: 48px;

        .audio-player {
          height: 48px;
          background: #ffffff;
        }

        .audio-controls {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .audio-progress {
          flex: 1;
          height: 4px;
          background-color: #e0e0e0;
          border-radius: 2px;
          position: relative;
        }

        .audio-time {
          position: absolute;
          right: 0;
          top: -20px;
          font-size: 0.9rem;
          color: #666;
        }
      }
    }
  }
}

.transcript-container {
  max-height: 400px; /* Adjust the height as needed */
  overflow-y: auto;
  overflow-x: hidden; /* Disable horizontal scrolling */
  gap: 8px;
  padding: 16px;
}

.transcript-container::-webkit-scrollbar {
  width: 8px;
}

.transcript-container::-webkit-scrollbar-thumb {
  background: #09196b;
  border-radius: 20px;
}

.transcript-container::-webkit-scrollbar-track {
  background: transparent;
}
