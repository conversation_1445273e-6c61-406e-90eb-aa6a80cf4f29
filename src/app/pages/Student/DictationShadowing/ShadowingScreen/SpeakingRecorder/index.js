import React, {useEffect, useRef, useState, forwardRef, useImperativeHandle} from 'react';
import {connect, useDispatch} from 'react-redux';
import {useTranslation} from 'react-i18next';
import {io} from 'socket.io-client';
import {toast} from '@component/ToastProvider';
import AntButton from '@component/AntButton';
import {CONSTANT, BUTTON, RECOGNIZE_STATUS, RECORD_STATUS} from '@constant';
import {renderAudioDuration} from '@common/functionCommons';
import Microphone from '@component/SvgIcons/Microphone';
import STOP_INNER from '@src/asset/icon/stop-inner.svg';
import {useShadowing} from '../ShadowingScreen';

import './SpeakingRecorder.scss';
import {actions, TRACKING_ACTIONS} from '@src/ducks/tracking.duck';

const SpeakingRecorder = forwardRef(({user, studentTools}, ref) => {
  const {t} = useTranslation();
  const {handleDataResponse} = useShadowing();
  const {setRecordResult, setText} = useShadowing();
  const {setWordAnalyzed, setCountErrors} = useShadowing();
  const {setAudioFileId, setAudioUrlDownload} = useShadowing();
  const {recognizeStatus, setRecognizeStatus} = useShadowing();
  const {disabledEdit} = useShadowing();
  const {currentQuestion, exerciseData} = useShadowing();

  const socket = useRef(null);

  const [recordStatus, setRecordStatus] = useState(RECORD_STATUS.STOPPED);
  const timeRef = useRef(null);
  const [timeRecorded, setTimeRecorded] = useState(0);

  const [hasMicAccess, setHasMicAccess] = useState(true);
  //microphone_not_accessible

  const dispatch = useDispatch();

  const audioSourceRef = useRef(null);
  const scriptProcessorRef = useRef(null);

  useEffect(() => {
    // Dọn dẹp khi component unmount
    return () => {
      if (socket.current) socket.current.disconnect();
    };
  }, []);

  useEffect(() => {
    if (timeRecorded >= 10) {
      setRecordStatus(RECORD_STATUS.STOPPED);
    }
  }, [timeRecorded]);

  useImperativeHandle(ref, () => ({
    startRecording: () => {
      if ([RECORD_STATUS.STOPPED].includes(recordStatus) && [RECOGNIZE_STATUS.NOT_STARTED, RECOGNIZE_STATUS.COMPLETE].includes(recognizeStatus)) {
        console.log("Triggering startRecording via ref");
        handleStartRecord();
      }
    },
    stopRecording: () => {
      if (recordStatus === RECORD_STATUS.RECORDING) {
        console.log("Triggering stopRecording via ref");
        handleStopRecordClick();
      }
    }
  }));

  async function handleStartRecord() {
    if (recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING || disabledEdit || recordStatus !== RECORD_STATUS.STOPPED || recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING) {
      console.log("Prevented start: Already recording or recognizing.");
      return;
    }

    setRecognizeStatus(RECOGNIZE_STATUS.RECOGNIZING);
    setRecordStatus(RECORD_STATUS.PREPARE_RECORD);
    setRecordResult([]);
    setText('');

    setWordAnalyzed([]);
    setCountErrors({});

    setAudioFileId('');
    setAudioUrlDownload('');
    setTimeRecorded(0);
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.INPUT_ESSAY));
  }

  function handleStopRecord() {
    if (recordStatus === RECORD_STATUS.STOPPED && recognizeStatus !== RECOGNIZE_STATUS.RECOGNIZING) {
      console.log("Prevented stop: Not recording or already stopped/completed.");
      return;
    }

    clearInterval(timeRef.current);
    setRecordStatus(RECORD_STATUS.STOPPED);

    console.log('stopRecording');
    if (scriptProcessorRef.current) {
      scriptProcessorRef.current.onaudioprocess = null; // Hủy bỏ callback để ngừng xử lý âm thanh
      scriptProcessorRef.current.disconnect(); // Ngừng kết nối scriptProcessor
    }

    if (audioSourceRef.current) {
      console.log('stopRecording audioSteam');
      audioSourceRef.current.disconnect();
      audioSourceRef.current.mediaStream?.getTracks()?.forEach(track => track.stop());
    }

    socket.current?.emit('close-recording');
  }

  useEffect(() => {
    console.log('recordStatus', recordStatus);

    switch (recordStatus) {
      case RECORD_STATUS.PREPARE_RECORD:
        handlePrepareRecord();
        break;
      case RECORD_STATUS.RECORDING:
        handleDataResponse(socket.current);
        handleRecordAudio();
        break;
      case RECORD_STATUS.STOPPED:
        handleStopRecord();
        break;
      default:
        break;
    }
  }, [recordStatus]);

  async function handlePrepareRecord() {

    socket.current = io('/speaking-advanced', {transports: ['websocket'], path: '/socket'});

    socket.current.on('connect', () => {
      console.log('WS connect');
    });

    socket.current.on('server_ready', () => {
      setRecordStatus(RECORD_STATUS.RECORDING);
    });

    socket.current.on('error', err => {
      console.log('socket error', err);
      socket.current.disconnect();
      setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
    });
  }

  async function handleRecordAudio() {

    const stream = await navigator.mediaDevices
      .getUserMedia({audio: true})
      .then(stream => {
        console.log('Đã truy cập micrô:', stream);
        return stream; // Trả về stream
      })
      .catch(error => {
        console.error('Lỗi truy cập micrô:', error);
        return null; // Trả về null nếu có lỗi
      });

    if (!stream) {
      setRecordStatus(RECORD_STATUS.STOPPED);
      setRecognizeStatus(RECOGNIZE_STATUS.NOT_STARTED);
      setHasMicAccess(false);
      return toast.error(t('MICROPHONE_NOT_ACCESSIBLE'), {replace: true});
    }

    // 2. Tạo AudioContext
    const audioContext = new AudioContext({sampleRate: 16000});

    // 3. Kết nối stream với AudioContext
    const source = audioContext.createMediaStreamSource(stream);
    audioSourceRef.current = source;
    // 4. Tạo ScriptProcessorNode
    const bufferSize = 4096; // Kích thước khối xử lý
    const scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);
    scriptProcessorRef.current = scriptProcessor;
    // 5. Xử lý dữ liệu PCM trong callback
    scriptProcessor.onaudioprocess = audioProcessingEvent => {
      const inputBuffer = audioProcessingEvent.inputBuffer;
      const channelData = inputBuffer.getChannelData(0); // Lấy dữ liệu kênh đầu tiên (mono)

      // Chuyển đổi Float32 (-1.0 -> 1.0) sang Int16 (-32768 -> 32767)
      const pcmInt16Array = new Int16Array(channelData.length);
      for (let i = 0; i < channelData.length; i++) {
        pcmInt16Array[i] = Math.max(-1, Math.min(1, channelData[i])) * 0x7fff;
      }

      // Tạo Uint8Array từ Int16Array để gửi qua socket
      const pcmUint8Array = new Uint8Array(pcmInt16Array.buffer);

      // Gửi dữ liệu qua socket
      // const inputData = {
      //   exerciseId: exerciseData._id,
      //   segment: currentQuestion.segment,
      // };

      const inputData = {
        sessionId: "682bf1d08c119c6e0ca13b6a",
        questionId: "682bf1d08c119c6e0ca13b72",
        part: "part1",
      };

      console.log('inputData', inputData);

      socket.current?.emit('audio', {
        buffer: pcmUint8Array,
        inputData,
        userId: user._id,
      });
    };

    // 6. Kết nối các nodes
    source.connect(scriptProcessor);
    scriptProcessor.connect(audioContext.destination);

    timeRef.current = setInterval(() => {
      setTimeRecorded(prevState => prevState + 1);
    }, 1000);
  }

  function renderRecorderText() {
    switch (recordStatus) {
      case RECORD_STATUS.STOPPED:
        if (recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING) return t('RECOGNIZING') + '...';
        return (
          <>
            {t('CLICK_TO_RECORD')}
            <br/>
            {t('MAXIMUM_10S')}
          </>
        );
      case RECORD_STATUS.PREPARE_RECORD:
        return t('PREPARING') + '...';
      case RECORD_STATUS.RECORDING:
        return t('RECORDING');
      default:
        return '';
    }
  }

  const handleStopRecordClick = () => {
    if (recordStatus === RECORD_STATUS.RECORDING) {
      setRecordStatus(RECORD_STATUS.STOPPED);
    }
  }

  return (
    <div className="speaking-record__recorder">
      <div className="speaking-record__recorder-icon">
        {recordStatus === RECORD_STATUS.STOPPED && (
          <AntButton
            size="large"
            shape="circle"
            type={BUTTON.DEEP_BLUE}
            icon={<Microphone/>}
            onClick={handleStartRecord}
            disabled={recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING || disabledEdit}
          />
        )}

        {recordStatus === RECORD_STATUS.PREPARE_RECORD && (
          <AntButton size="large" shape="circle" type={BUTTON.DEEP_BLUE} loading/>
        )}

        {recordStatus === RECORD_STATUS.RECORDING && (
          <AntButton
            size="large"
            shape="circle"
            type={BUTTON.DEEP_RED}
            icon={<img src={STOP_INNER} alt=""/>}
            onClick={handleStopRecordClick}
          />
        )}
      </div>
      <div className="speaking-record__recorder-text">{renderRecorderText()}</div>

      {recordStatus !== RECORD_STATUS.STOPPED && (
        <div className="speaking-record__recorder-time">{renderAudioDuration(timeRecorded)}</div>
      )}
    </div>
  );
});

function mapStateToProps(store) {
  const {user} = store.auth;
  const {studentTools} = store.tool;
  return {user, studentTools};
}

export default connect(mapStateToProps, null, null, {forwardRef: true})(SpeakingRecorder);
