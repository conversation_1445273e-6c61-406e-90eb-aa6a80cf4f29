import React, {useEffect, useState} from "react";
import {Col, Form, Input, Row, Select} from "antd";
import {useTranslation} from "react-i18next";
import {useDropzone} from "react-dropzone";
import {useParams, useNavigate} from "react-router-dom";

import AntButton from "@component/AntButton";

import {BUTTON} from "@constant";

import "./VoiceOptionDetail.scss";
import Loading from "@src/app/component/Loading";
import {createVoiceOption, updateVoiceOption, getVoiceOptionDetail, uploadVoice} from "@src/app/services/VoiceOptions";
import {LINK} from "@link";
import {toast} from "@src/app/component/ToastProvider";
import {API} from "@api";


const VoiceOptionDetail = ({...props}) => {
  const {t} = useTranslation();
  const [formOption] = Form.useForm();
  const {id} = useParams();
  const navigate = useNavigate();

  const [audioFile, setAudioFile] = useState(null);
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    if (id) {
      getVoiceOption();
    } else {
      formOption.resetFields();
    }
  }, [id]);

  const getVoiceOption = async () => {
    setLoading(true);
    const apiResponse = await getVoiceOptionDetail(id);
    if (apiResponse) {
      formOption.setFieldsValue(apiResponse);
      if (apiResponse.voiceFileId) {
        setAudioFile(apiResponse.voiceFileId);
      }
    }
    setLoading(false);
  };

  const onFinish = async (values) => {
    try {
      setLoading(true);
      const dataReques = id ? {...values, _id: id} : values;
      const apiResponse = await (id ? updateVoiceOption(dataReques) : createVoiceOption(dataReques));
      if (apiResponse) {
        toast.success(t(id ? "UPDATE_VOICE_OPTION_SUCCESS" : "CREATE_VOICE_OPTION_SUCCESS"));
        if (!id) {
          navigate(LINK.ADMIN_VOICE_OPTION_DETAIL.format(apiResponse._id));
        }
      }
    } catch (error) {
      toast.error(t(id ? "UPDATE_VOICE_OPTION_ERROR" : "CREATE_VOICE_OPTION_ERROR"));
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (files) => {
    const file = files[0];
    if (file) {
      try {
        setLoading(true);

        // Kiểm tra xem id có tồn tại không
        if (!id) {
          toast.error(t("SAVE_BEFORE_UPLOAD"));
          setLoading(false);
          return;
        }
        console.log("Basic InformationBasic InformationBasic InformationBasic InformationBasic Information")

        const apiResponse = await uploadVoice(id, file);
        console.log("apiResponseapiResponseapiResponseapiResponseapiResponse")
        if (apiResponse) {
          setAudioFile(apiResponse?.voiceFileId);
          toast.success(t("UPLOAD_FILE_SUCCESS"), {replace: true});
        } else {
          toast.error(t("UPLOAD_FILE_ERROR"));
        }
      } catch (error) {
        console.error("Upload error:", error);
        toast.error(t("UPLOAD_FILE_ERROR"));
      } finally {
        setLoading(false);
      }
    }
  }

  const {getRootProps, getInputProps, open} = useDropzone({
    onDrop: handleUpload,
    noClick: true,
    accept: {
      "audio/mp3": [".mp3"],
      "audio/wav": [".wav"],
    },
    multiple: false,
  });

  return (
    <Loading active={isLoading}>
      <div className="voice-option-detail">
        <div className="voice-option-detail__header">
          <div className="voice-option-detail__title">
            <h1>{id ? t("EDIT_VOICE_OPTION") : t("CREATE_VOICE_OPTION")}</h1>
            <p className="voice-option-detail__description">{t("VOICE_OPTION_DESCRIPTION")}</p>
          </div>
        </div>

        <div className="voice-option-detail__content">
          <div className="voice-option-detail__section">
            <div className="section-title">{t("BASIC_INFORMATION")}</div>
            <Form
              className="voice-option-form"
              onFinish={onFinish}
              layout="vertical"
              size="large"
              form={formOption}
            >
              <Row gutter={24}>
                <Col xs={24} lg={12}>
                  <Form.Item
                    label={t("NAME")}
                    name="name"
                    rules={[{required: true, message: t("NAME_REQUIRED")}]}
                  >
                    <Input placeholder={t("ENTER_NAME")}/>
                  </Form.Item>
                </Col>
                <Col xs={24} lg={12}>
                  <Form.Item
                    label={t("CODE")}
                    name="code"
                    rules={[{required: true, message: t("CODE_REQUIRED")}]}
                  >
                    <Input placeholder={t("ENTER_CODE")}/>
                  </Form.Item>
                </Col>
                <Col xs={24} lg={12}>
                  <Form.Item
                    label={t("IS_PUBLIC")}
                    name="isPublic"
                    rules={[{required: true, message: t("IS_PUBLIC_REQUIRED")}]}
                  >
                    <Select
                      options={[
                        {value: true, label: t("YES")},
                        {value: false, label: t("NO")},
                      ]}
                      placeholder={t("SELECT_IS_PUBLIC")}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <div className="form-actions">
                <AntButton
                  type={BUTTON.DEEP_NAVY}
                  size="large"
                  onClick={() => formOption.submit()}
                >
                  {t("SAVE")}
                </AntButton>
              </div>
            </Form>
          </div>

          {id && (
            <div className="voice-option-detail__section">
              <div className="section-title">{t("VOICE_FILE")}</div>
              <div className="voice-option-detail__audio">
                <div className="voice-upload-container">
                  <div className="voice-upload-dropzone" {...getRootProps()}>
                    <input {...getInputProps()} />
                    {!audioFile ? (
                      <div className="voice-upload-placeholder">
                        <div className="voice-upload-icon">
                          <i className="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div className="voice-upload-text">
                          <p>{t("DRAG_DROP_AUDIO")}</p>
                          <span>{t("OR")}</span>
                          <AntButton type={BUTTON.DEEP_NAVY} size="large" onClick={open}>
                            {t("BROWSE_FILES")}
                          </AntButton>
                        </div>
                        <div className="voice-upload-formats">
                          <p>{t("SUPPORTED_FORMATS")}: MP3, WAV</p>
                        </div>
                      </div>
                    ) : (
                      <div className="voice-file-preview">
                        <div className="voice-file-info">
                          <div className="voice-file-icon">
                            <i className="fas fa-file-audio"></i>
                          </div>
                          <div className="voice-file-details">
                            <div className="voice-file-name">{audioFile?.displayName}</div>
                          </div>
                        </div>
                        <div className="voice-file-actions">
                          <audio
                            controls
                            className="voice-audio-player"
                            src={API.STREAM_MEDIA.format(audioFile._id)}
                          />
                          <div className="voice-file-buttons">
                            <AntButton type={BUTTON.LIGHT_NAVY} size="large" onClick={open}>
                              {t("CHANGE_FILE")}
                            </AntButton>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}


        </div>
      </div>
    </Loading>
  );
};

export default VoiceOptionDetail;
