import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Card, Form, Input, Row, Col, Select, Tag, Tooltip } from "antd";
import { SearchOutlined, EditOutlined, PlusOutlined, EyeOutlined, DeleteOutlined } from "@ant-design/icons";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import { LINK } from "@link";
import { BUTTON, PAGINATION_INIT } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, orderColumn, paginationConfig, handleReplaceUrlSearch, formatTimeDate } from "@common/functionCommons";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import "../EmailMarketing.scss";

import { getPaginationTemplates, deleteTemplate } from "@services/EmailMarketing";

const EmailTemplate = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [templateData, setTemplateData] = useState(PAGINATION_INIT);
  const [isLoading, setLoading] = useState(false);

  const [formFilter] = Form.useForm();

  // Định nghĩa các danh mục mẫu email
  const TEMPLATE_CATEGORIES = [
    { value: "welcome", label: t("TEMPLATE_CATEGORY_WELCOME") },
    { value: "promotion", label: t("TEMPLATE_CATEGORY_PROMOTION") },
    { value: "reminder", label: t("TEMPLATE_CATEGORY_REMINDER") },
    { value: "notification", label: t("TEMPLATE_CATEGORY_NOTIFICATION") },
    { value: "other", label: t("TEMPLATE_CATEGORY_OTHER") }
  ];

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getTemplateData(paging, query);
  }, [location.search]);

  const getTemplateData = async (paging = templateData.paging, query = templateData.query) => {
    setLoading(true);
    try {
      const dataResponse = await getPaginationTemplates(paging, query);
      if (dataResponse) {
        setTemplateData(handlePagingData(dataResponse, query));
      }
    } catch (error) {
      console.error("Error fetching templates:", error);
      toast.error(t("ERROR_FETCHING_TEMPLATES"));
    } finally {
      setLoading(false);
    }
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, templateData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, templateData.paging.pageSize, {});
  };

  const handleDelete = (templateId, templateName) => {
    confirm({
      title: t("CONFIRM_DELETE_TEMPLATE"),
      content: t("CONFIRM_DELETE_TEMPLATE_CONTENT", { name: templateName }),
      onOk: async () => {
        setLoading(true);
        try {
          const apiResponse = await deleteTemplate(templateId);
          if (apiResponse) {
            toast.success(t("DELETE_TEMPLATE_SUCCESS"));
            await getTemplateData();
          } else {
            toast.error(t("DELETE_TEMPLATE_ERROR"));
          }
        } catch (error) {
          console.error("Error deleting template:", error);
          toast.error(t("DELETE_TEMPLATE_ERROR"));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // Định nghĩa các cột cho bảng
  const columns = [
    {
      ...orderColumn(templateData.paging),
      width: 80,
    },
    {
      title: t("NAME"),
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (text) => <span className="name-value">{text}</span>,
    },
    {
      title: t("SUBJECT"),
      dataIndex: "subject",
      key: "subject",
      width: 300,
      render: (text) => text,
    },
    {
      title: t("CATEGORY"),
      dataIndex: "category",
      key: "category",
      width: 150,
      render: (text) => {
        let color;
        switch (text) {
          case "welcome":
            color = "green";
            break;
          case "promotion":
            color = "blue";
            break;
          case "reminder":
            color = "orange";
            break;
          case "notification":
            color = "purple";
            break;
          default:
            color = "default";
        }
        const categoryLabel = TEMPLATE_CATEGORIES.find(item => item.value === text)?.label || text;
        return <Tag color={color}>{categoryLabel}</Tag>;
      },
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (text) => formatTimeDate(text),
    },
    {
      title: t("ACTION"),
      width: 180,
      align: "center",
      render: (_, record) => (
        <div className="email-marketing-actions">
          <Tooltip title={t("PREVIEW_TEMPLATE")}>
            <Link to={`${LINK.ADMIN.EMAIL_TEMPLATE_ID.format(record._id)}/preview`}>
              <AntButton
                className="btn-preview"
                icon={<EyeOutlined/>}
                size="small"
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("EDIT_TEMPLATE")}>
            <Link to={LINK.ADMIN.EMAIL_TEMPLATE_ID.format(record._id)}>
              <AntButton
                className="btn-edit"
                icon={<EditOutlined/>}
                size="small"
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_TEMPLATE")}>
            <AntButton
              danger
              className="btn-delete"
              icon={<DeleteOutlined/>}
              onClick={() => handleDelete(record?._id, record?.name)}
              size="small"
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(templateData.paging, templateData.query, i18n.language);

  return (
    <Loading active={isLoading} transparent>
      <div className="email-marketing-container">
        <Card className="email-marketing-info-card">
          <div className="email-marketing-info-header">
            <div>
              <h1 className="email-marketing-title">{t("EMAIL_TEMPLATE_MANAGEMENT")}</h1>
              <p className="email-marketing-description">{t("EMAIL_TEMPLATE_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN.EMAIL_TEMPLATE_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_TEMPLATE")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="email-marketing-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_TEMPLATE_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="subject" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_SUBJECT_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="category" className="search-form-item">
                      <Select
                        options={TEMPLATE_CATEGORIES}
                        placeholder={t("FILTER_BY_CATEGORY")}
                        allowClear
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="email-marketing-table-card">
          <TableAdmin
            columns={columns}
            dataSource={templateData.rows}
            pagination={{ ...pagination }}
            scroll={{ x: 1000 }}
            className={"email-marketing-table"}
            rowClassName={() => "email-marketing-table-row"}
            locale={{ emptyText: t("NO_TEMPLATES_FOUND") }}
            rowKey="_id"
          />
        </Card>
      </div>
    </Loading>
  );
};

export default EmailTemplate;
