.email-template-detail-container {
  background-color: var(--white);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  .email-template-detail__header {
    padding: 16px 20px;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    display: flex;
    align-items: center;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .header-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .header-title {
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
        color: var(--typo-colours-primary-black);
      }

      .header-description {
        font-size: 14px;
        line-height: 20px;
        color: var(--typo-colours-secondary-grey);
      }
    }
  }

  // Shared styles for both template info and content containers
  .template-info-container,
  .template-content-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    }

    .info-header {
      background-color: #f8fafc;
      padding: 20px 32px;
      border-bottom: 1px solid #edf2f7;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f1f1f;
        margin: 0;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 18px;
          background-color: var(--primary-color);
          margin-right: 12px;
          border-radius: 2px;
        }
      }
    }

    .info-content {
      padding: 32px;

      // Shared form styles
      .form-template-info {
        display: flex;
        flex-direction: column;
        gap: 30px;

        .form-row {
          display: flex;
          flex-direction: row;
          gap: 24px;
          align-items: flex-start;

          .title-field {
            flex: 2;
          }

          .category-field {
            flex: 1;
          }
        }

        .ant-form-item {
          margin: 0;
          width: 100%;

          .ant-form-item-label > label {
            font-weight: 600;
            color: #333;
            font-size: 15px;
          }

          .ant-input, .ant-select {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s;

            &:hover, &:focus {
              border-color: var(--primary-color);
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
            }
          }

          .ant-input {
            height: 46px;
          }

          .ant-select-selector {
            border-radius: 8px;
            height: 46px !important;
            display: flex;
            align-items: center;
          }

          .ant-select-selection-item {
            line-height: 46px;
          }
        }
      }
    }
  }

  // Email content specific styles
  .template-content-container {
    // Card styling for all cards
    .sample-template-card,
    .email-variables-card,
    .email-editor-card {
      margin-bottom: 24px;
      border-radius: 12px;
      border: 1px solid #e8e8e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border-color: #d9d9d9;
      }

      .ant-card-body {
        padding: 20px;
      }

      .card-header {
        margin-bottom: 16px;

        .card-title {
          display: flex;
          align-items: center;
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;

          .card-icon {
            margin-right: 8px;
            font-size: 18px;
          }
        }

        .card-description {
          margin: 0;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }

        .card-actions {
          display: flex;
          align-items: center;
          gap: 12px;
        }
      }
    }

    // Sample template card specific
    .sample-template-card {
      .sample-template-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;

        .sample-template-card-button {
          height: 60px;
          border-radius: 8px;
          border: 2px solid #e8e8e8;
          background-color: #fafafa;
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            background-color: #f0f8ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
          }

          .template-button-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .template-icon {
              font-size: 20px;
            }

            .template-label {
              font-size: 12px;
              font-weight: 500;
              color: #333;
            }
          }
        }

        // Empty state styling
        .no-category-message,
        .no-templates-message {
          grid-column: 1 / -1;
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 120px;
          padding: 20px;

          .empty-state {
            text-align: center;
            color: #8c8c8c;

            .empty-icon {
              font-size: 32px;
              display: block;
              margin-bottom: 12px;
            }

            .empty-text {
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 8px;
              color: #595959;
            }

            .empty-hint {
              font-size: 14px;
              color: #8c8c8c;
              margin: 0;
            }
          }
        }
      }
    }

    // Email variables card specific
    .email-variables-card {
      .email-variables-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 12px;

        .variable-item-card {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          background-color: #fafafa;
          border-radius: 8px;
          border: 1px solid #e8e8e8;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f0f8ff;
            border-color: #91d5ff;
          }

          .variable-button {
            background-color: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
            font-family: 'Courier New', monospace;
            min-width: 100px;
            font-size: 12px;
            font-weight: 600;
            border-radius: 6px;

            &:hover {
              background-color: #1890ff;
              border-color: #1890ff;
              color: white;
              transform: translateY(-1px);
              box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
            }
          }

          .variable-description {
            font-size: 13px;
            color: #666;
            flex: 1;
            line-height: 1.4;
          }
        }
      }
    }

    // Email editor card specific
    .email-editor-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;

        .card-title {
          margin-bottom: 0;
        }
      }

      .email-editor-content {
        .email-preview-container {
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          background-color: #fafafa;
          padding: 16px;
          min-height: 300px;

          .email-preview {
            background-color: white;
            padding: 20px;
            border-radius: 6px;
            min-height: 250px;
            font-family: Arial, sans-serif;
            line-height: 1.6;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }
        }

        .email-content-editor {
          border-radius: 8px;
          border: 1px solid #e8e8e8;
          font-family: 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.5;

          &:hover, &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
      }
    }

    .test-email-container {
      padding: 16px 0;

      .test-email-label {
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
        font-size: 15px;
      }

      .test-email-input-group {
        display: flex;
        gap: 16px;

        .test-email-input {
          flex: 1;
          height: 46px;
          border-radius: 8px;
          border: 1px solid #e2e8f0;

          &:hover, &:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }

        .test-email-button {
          min-width: 150px;
          border-radius: 8px;
          height: 46px;
          padding: 0 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .email-preview-toggle {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 16px;

      .preview-toggle-button {
        border-radius: 8px;
        height: 40px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .email-content-editor {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      min-height: 300px;
      font-family: monospace;
      padding: 16px;

      &:hover, &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }

    .email-preview {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 16px;
      min-height: 300px;
      background-color: #fff;
      overflow: auto;
    }
  }

  .save-button-container {
    display: flex;
    justify-content: center;
    margin: 24px 0;
    width: 100%;

    .save-button,
    .cancel-button {
      min-width: 180px;
      height: 48px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border-radius: 6px;
      transition: all 0.3s ease;
    }

    .save-button {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }

    .cancel-button {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  // Preview Modal Styles
  .email-preview-modal {
    .ant-modal-header {
      border-bottom: 1px solid #e8e8e8;
      padding: 20px 24px;

      .modal-header {
        display: flex;
        align-items: center;

        .modal-icon {
          margin-right: 8px;
          font-size: 20px;
        }

        .modal-title {
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .ant-modal-body {
      padding: 24px;
    }

    .ant-modal-footer {
      border-top: 1px solid #e8e8e8;
      padding: 16px 24px;
      text-align: right;

      .modal-close-button {
        min-width: 120px;
        height: 40px;
        border-radius: 6px;
      }
    }

    .preview-modal-content {
      .preview-data-card,
      .preview-email-card {
        margin-bottom: 24px;
        border-radius: 12px;
        border: 1px solid #e8e8e8;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .ant-card-body {
          padding: 20px;
        }

        .card-header {
          margin-bottom: 16px;

          .card-title {
            display: flex;
            align-items: center;
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;

            .card-icon {
              margin-right: 8px;
              font-size: 18px;
            }
          }

          .card-description {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }

      .preview-data-card {
        .preview-form-content {
          .preview-input-group {
            margin-bottom: 16px;

            .preview-label {
              display: block;
              margin-bottom: 6px;
              font-size: 13px;
              color: #666;
              font-weight: 500;

              .variable-name {
                font-family: 'Courier New', monospace;
                background-color: #e6f7ff;
                padding: 2px 6px;
                border-radius: 4px;
                color: #1890ff;
                font-weight: 600;
                font-size: 12px;
              }

              .variable-desc {
                color: #666;
              }
            }

            .preview-input {
              border-radius: 6px;
              border: 1px solid #d9d9d9;
              height: 36px;

              &:hover, &:focus {
                border-color: #1890ff;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
              }
            }
          }

          .no-variables-message {
            .empty-state {
              text-align: center;
              padding: 40px 20px;
              background-color: #fafafa;
              border-radius: 8px;
              border: 2px dashed #d9d9d9;

              .empty-icon {
                font-size: 48px;
                display: block;
                margin-bottom: 16px;
              }

              .empty-text {
                font-size: 16px;
                color: #666;
                margin-bottom: 8px;
                font-weight: 500;
              }

              .empty-hint {
                font-size: 14px;
                color: #999;
                margin: 0;
              }
            }
          }
        }
      }
    }
  }

  .preview-email-content {
    h4 {
      margin-bottom: 16px;
      color: #333;
      font-weight: 600;
    }

    .preview-subject {
      margin-bottom: 16px;
      padding: 12px;
      background-color: #f0f2f5;
      border-radius: 6px;
      border-left: 4px solid #1890ff;

      strong {
        color: #333;
      }

      span {
        color: #666;
        margin-left: 8px;
      }
    }

    .preview-email-card {
      .email-mockup {
        border: 1px solid #e8e8e8;
        border-radius: 12px;
        background-color: #ffffff;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        overflow: hidden;

        .email-header {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          padding: 20px;
          border-bottom: 1px solid #e8e8e8;

          .email-meta-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;

            &:last-child {
              margin-bottom: 0;
            }

            div {
              strong {
                color: #333;
                margin-right: 8px;
              }

              color: #666;
            }
          }

          .email-subject-row {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #e8e8e8;

            .email-subject {
              font-size: 14px;
              font-weight: 600;
              color: #1890ff;

              strong {
                color: #333;
                margin-right: 8px;
              }
            }
          }
        }

        .email-body {
          padding: 24px;

          .email-content-preview {
            min-height: 250px;
            max-height: 600px;
            overflow-y: auto;
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;

            // Email content styling
            h1, h2, h3, h4, h5, h6 {
              margin-top: 0;
              margin-bottom: 16px;
              color: #333;
            }

            p {
              margin-bottom: 12px;
              color: #555;
            }

            a {
              color: #1890ff;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }

            img {
              max-width: 100%;
              height: auto;
            }

            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 16px;

              th, td {
                padding: 8px 12px;
                border: 1px solid #e8e8e8;
                text-align: left;
              }

              th {
                background-color: #f5f5f5;
                font-weight: 600;
              }
            }

            ul, ol {
              margin-bottom: 12px;
              padding-left: 20px;

              li {
                margin-bottom: 4px;
              }
            }

            blockquote {
              margin: 16px 0;
              padding: 12px 16px;
              background-color: #f8f9fa;
              border-left: 4px solid #1890ff;
              font-style: italic;
            }
          }

          .email-footer {
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid #e8e8e8;
          }
        }
      }
    }
  }
}
