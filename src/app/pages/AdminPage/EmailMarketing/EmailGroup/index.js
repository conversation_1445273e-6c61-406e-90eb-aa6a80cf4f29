import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Card, Form, Input, Row, Col, Select, Tag, Tooltip, Button } from "antd";
import { SearchOutlined, EditOutlined, PlusOutlined, DeleteOutlined } from "@ant-design/icons";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import { LINK } from "@link";
import { BUTTON, PAGINATION_INIT } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, orderColumn, paginationConfig, handleReplaceUrlSearch, formatTimeDate } from "@common/functionCommons";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import "../EmailMarketing.scss";

import { getPaginationGroups, deleteGroup } from "@services/EmailMarketing";

// Định nghĩa các loại nhóm
const GROUP_TYPES = [
  { value: "automatic", label: "Tự động" },
  { value: "manual", label: "Thủ công" }
];

const EmailGroup = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [groupData, setGroupData] = useState(PAGINATION_INIT);
  const [isLoading, setLoading] = useState(false);

  const [formFilter] = Form.useForm();

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getGroupData(paging, query);
  }, [location.search]);

  const getGroupData = async (paging = groupData.paging, query = groupData.query) => {
    setLoading(true);
    try {
      const dataResponse = await getPaginationGroups(paging, query);
      if (dataResponse) {
        setGroupData(handlePagingData(dataResponse, query));
      }
    } catch (error) {
      console.error("Error fetching groups:", error);
      toast.error(t("ERROR_FETCHING_GROUPS"));
    } finally {
      setLoading(false);
    }
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, groupData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, groupData.paging.pageSize, {});
  };

  const handleDelete = (groupId, groupName) => {
    confirm.delete({
      title: t("DELETE_GROUP"),
      content: t("DELETE_GROUP_CONFIRM", { name: groupName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setLoading(true);
        try {
          const apiResponse = await deleteGroup(groupId);
          if (apiResponse) {
            toast.success(t("DELETE_GROUP_SUCCESS"));
            await getGroupData();
          } else {
            toast.error(t("DELETE_GROUP_ERROR"));
          }
        } catch (error) {
          console.error("Error deleting group:", error);
          toast.error(t("DELETE_GROUP_ERROR"));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleRefresh = async (groupId, groupName) => {
    setLoading(true);
    try {
      const apiResponse = await refreshGroup(groupId);
      if (apiResponse) {
        toast.success(t("REFRESH_GROUP_SUCCESS", { name: groupName }));
        await getGroupData();
      } else {
        toast.error(t("REFRESH_GROUP_ERROR"));
      }
    } catch (error) {
      console.error("Error refreshing group:", error);
      toast.error(t("REFRESH_GROUP_ERROR"));
    } finally {
      setLoading(false);
    }
  };

  // Định nghĩa các cột cho bảng
  const columns = [
    {
      ...orderColumn(groupData.paging),
      width: 80,
    },
    {
      title: t("NAME"),
      dataIndex: "name",
      key: "name",
      width: 250,
      render: (text) => <span className="name-value">{text}</span>,
    },
    {
      title: t("TYPE"),
      dataIndex: "type",
      key: "type",
      width: 150,
      render: (text) => {
        let color = text === "automatic" ? "blue" : "purple";
        const typeLabel = GROUP_TYPES.find(item => item.value === text)?.label || text;
        return <Tag color={color}>{typeLabel}</Tag>;
      },
    },
    {
      title: t("USER_COUNT"),
      dataIndex: "userCount",
      key: "userCount",
      width: 150,
      render: (text) => text,
    },
    {
      title: t("UPDATED_AT"),
      dataIndex: "updatedAt",
      key: "updatedAt",
      width: 150,
      render: (text) => formatTimeDate(text),
    },
    {
      title: t("ACTION"),
      width: 180,
      align: "center",
      render: (_, record) => (
        <div className="email-marketing-actions">
          <Tooltip title={t("EDIT_GROUP")}>
            <Link to={LINK.ADMIN.EMAIL_GROUP_ID.format(record._id)}>
              <AntButton
                className="btn-edit"
                icon={<EditOutlined/>}
                size="small"
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_GROUP")}>
            <AntButton
              danger
              className="btn-delete"
              icon={<DeleteOutlined/>}
              onClick={() => handleDelete(record?._id, record?.name)}
              size="small"
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(groupData.paging, groupData.query, i18n.language);

  return (
    <Loading active={isLoading} transparent>
      <div className="email-marketing-container">
        <Card className="email-marketing-info-card">
          <div className="email-marketing-info-header">
            <div>
              <h1 className="email-marketing-title">{t("EMAIL_GROUP_MANAGEMENT")}</h1>
              <p className="email-marketing-description">{t("EMAIL_GROUP_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN.EMAIL_GROUP_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_GROUP")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="email-marketing-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_GROUP_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="type" className="search-form-item">
                      <Select
                        options={GROUP_TYPES}
                        placeholder={t("FILTER_BY_TYPE")}
                        allowClear
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="email-marketing-table-card">
          <TableAdmin
            columns={columns}
            dataSource={groupData.rows}
            pagination={{ ...pagination }}
            scroll={{ x: 1000 }}
            className={"email-marketing-table"}
            rowClassName={() => "email-marketing-table-row"}
            locale={{ emptyText: t("NO_GROUPS_FOUND") }}
            rowKey="_id"
          />
        </Card>
      </div>
    </Loading>
  );
};

export default EmailGroup;
