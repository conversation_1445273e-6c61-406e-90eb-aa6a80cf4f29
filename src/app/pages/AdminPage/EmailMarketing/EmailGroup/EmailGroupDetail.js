import React, {useEffect, useState} from "react";
import {useNavigate, useParams} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {Form, Input, Select, DatePicker, Row, Col, Radio, InputNumber, message} from "antd";
import {MinusCircleOutlined, PlusOutlined, SaveOutlined} from "@ant-design/icons";
import moment from "moment";
import {toast} from "@component/ToastProvider";
import Loading from "@component/Loading";
import {AntForm} from "@component/AntForm";
import AntButton from "@component/AntButton";
import RULE from "@rule";
import {LINK} from "@link";
import {BUTTON} from "@constant";
import "../EmailMarketing.scss";
import "./EmailGroupDetail.scss";

import {getGroupDetail, createGroup, updateGroup} from "@services/EmailMarketing";

// <PERSON><PERSON><PERSON> nghĩa các loạ<PERSON> nhóm
const GROUP_TYPES = [
  {value: "automatic", label: "Tự động"},
  {value: "manual", label: "Th<PERSON> công"}
];

// <PERSON><PERSON><PERSON> nghĩa các loại nhóm tự động
const AUTOMATIC_TYPES = [
  {value: "A1", label: "Người dùng mới"},
  {value: "A2", label: "Người dùng không hoạt động"},
  {value: "A3", label: "Người dùng thường xuyên"},
  {value: "A4", label: "Người dùng sắp hết hạn gói"},
  {value: "A5", label: "Người dùng đã hết hạn gói"},
  {value: "A6", label: "Người dùng đã sử dụng khuyến mãi"}
];

// Định nghĩa các trạng thái phản hồi
const RESPONSE_STATUS = [
  {value: "responded", label: "Đã phản hồi"},
  {value: "not_responded", label: "Chưa phản hồi"}
];

// Định nghĩa các loại gói
const PACKAGE_TYPES = [
  {value: "basic", label: "Gói cơ bản"},
  {value: "premium", label: "Gói cao cấp"},
  {value: "enterprise", label: "Gói doanh nghiệp"}
];

// Định nghĩa các mã khuyến mãi
const PROMOTION_CODES = [
  {value: "WELCOME2025", label: "WELCOME2025"},
  {value: "SUMMER25", label: "SUMMER25"},
  {value: "BLACKFRIDAY", label: "BLACKFRIDAY"}
];

const EmailGroupDetail = () => {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const {id} = useParams();

  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [groupType, setGroupType] = useState("automatic");

  // Lấy dữ liệu nhóm khi id thay đổi
  useEffect(() => {
    if (id && id !== "create") {
      getGroupData();
    } else {
      // Đặt giá trị mặc định cho nhóm mới
      form.setFieldsValue({
        type: "automatic",
        automaticType: "A1"
      });
      setGroupType("automatic");
      setLoading(false);
    }
  }, [id]);

  const getGroupData = async () => {
    setLoading(true);
    try {
      const data = await getGroupDetail(id);
      if (data) {
        // Chuẩn bị dữ liệu cho form
        const formData = {
          ...data,
          registrationDateRange: data.conditions?.registrationDateRange ?
            [data.conditions.registrationDateRange[0] ? moment(data.conditions.registrationDateRange[0]) : null,
              data.conditions.registrationDateRange[1] ? moment(data.conditions.registrationDateRange[1]) : null] : null,
          paidRegistrationDateRange: data.conditions?.paidRegistrationDateRange ?
            [data.conditions.paidRegistrationDateRange[0] ? moment(data.conditions.paidRegistrationDateRange[0]) : null,
              data.conditions.paidRegistrationDateRange[1] ? moment(data.conditions.paidRegistrationDateRange[1]) : null] : null,
          packageExpirationDateRange: data.conditions?.packageExpirationDateRange ?
            [data.conditions.packageExpirationDateRange[0] ? moment(data.conditions.packageExpirationDateRange[0]) : null,
              data.conditions.packageExpirationDateRange[1] ? moment(data.conditions.packageExpirationDateRange[1]) : null] : null,
          responseStatus: data.conditions?.responseStatus,
          usageDaysRange: data.conditions?.usageDaysRange || [null, null],
          inactiveDaysRange: data.conditions?.inactiveDaysRange || [null, null],
          packageTypes: data.conditions?.packageTypes,
          usedPromotionCodes: data.conditions?.usedPromotionCodes
        };

        form.setFieldsValue(formData);
        setGroupType(data.type);
      } else {
        message.error(t("FAILED_TO_LOAD_GROUP_DATA"));
      }
    } catch (error) {
      console.error('Error loading group data:', error);
      message.error(t("ERROR_LOADING_GROUP_DATA"));
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      // Validate form trước khi submit
      const values = await form.validateFields();
      setLoading(true);

      let res;

      // Chuẩn bị dữ liệu để lưu
      const saveData = {
        ...values,
        conditions: {
          registrationDateRange: values.registrationDateRange ?
            [values.registrationDateRange[0]?.format('YYYY-MM-DD'), values.registrationDateRange[1]?.format('YYYY-MM-DD')] : null,
          paidRegistrationDateRange: values.paidRegistrationDateRange ?
            [values.paidRegistrationDateRange[0]?.format('YYYY-MM-DD'), values.paidRegistrationDateRange[1]?.format('YYYY-MM-DD')] : null,
          packageExpirationDateRange: values.packageExpirationDateRange ?
            [values.packageExpirationDateRange[0]?.format('YYYY-MM-DD'), values.packageExpirationDateRange[1]?.format('YYYY-MM-DD')] : null,
          responseStatus: values.responseStatus,
          usageDaysRange: values.usageDaysRange && values.usageDaysRange.filter(v => v !== null && v !== undefined).length > 0 ? values.usageDaysRange : null,
          inactiveDaysRange: values.inactiveDaysRange && values.inactiveDaysRange.filter(v => v !== null && v !== undefined).length > 0 ? values.inactiveDaysRange : null,
          packageTypes: values.packageTypes,
          usedPromotionCodes: values.usedPromotionCodes
        }
      };

      // Xóa các trường không cần thiết
      delete saveData.registrationDateRange;
      delete saveData.paidRegistrationDateRange;
      delete saveData.packageExpirationDateRange;
      delete saveData.responseStatus;
      delete saveData.usageDaysRange;
      delete saveData.inactiveDaysRange;
      delete saveData.packageTypes;
      delete saveData.usedPromotionCodes;

      if (id && id !== "create") {
        saveData._id = id;

        try {
          res = await updateGroup(saveData);
          if (res) {
            toast.success(t("UPDATE_GROUP_SUCCESS"));
            // Reload dữ liệu sau khi update thành công
            await getGroupData();
          } else {
            toast.error(t("UPDATE_GROUP_ERROR"));
          }
        } catch (updateError) {
          toast.error(t("UPDATE_GROUP_ERROR"));
        }
      } else {
        try {
          res = await createGroup(saveData);
          if (res) {
            toast.success(t("CREATE_GROUP_SUCCESS"));
            // Chuyển đến trang edit với ID mới được tạo
            navigate(LINK.ADMIN.EMAIL_GROUP_ID.format(res._id || res.id));
          } else {
            toast.error(t("CREATE_GROUP_ERROR"));
          }
        } catch (createError) {
          toast.error(t("CREATE_GROUP_ERROR"));
        }
      }
    } catch (error) {
      if (error.errorFields) {
        // Form validation error
        toast.error(t("FORM_VALIDATION_ERROR"));
      } else {
        // API error
        toast.error(t("ERROR_SAVING_GROUP"));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGroupTypeChange = (e) => {
    const newType = e.target.value;
    setGroupType(newType);

    // Reset các field không cần thiết khi chuyển đổi loại nhóm
    if (newType === "automatic") {
      // Reset manual fields
      form.setFieldsValue({
        registrationDateRange: null,
        paidRegistrationDateRange: null,
        packageExpirationDateRange: null,
        responseStatus: null,
        usageDaysRange: [null, null],
        inactiveDaysRange: [null, null],
        packageTypes: [],
        usedPromotionCodes: []
      });
    } else if (newType === "manual") {
      // Reset automatic fields
      form.setFieldsValue({
        automaticType: null
      });
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.EMAIL_GROUP);
  };

  const isCreating = !id || id === "create";

  return (
    <Loading active={isLoading} transparent>
      <div className="email-group-detail-container">
        <div className="email-group-detail__header">
          <div className="header-content">
            <div className="header-title">{isCreating ? t("CREATE_NEW_GROUP") : t("EDIT_GROUP")}</div>
            <div className="header-description">{t("EMAIL_GROUP_MANAGEMENT_DESCRIPTION")}</div>
          </div>
        </div>

        <div className="group-info-container">
          <div className="info-header">
            <h3 className="section-title">{t("GROUP_INFORMATION")}</h3>
          </div>
          <div className="info-content">
            <AntForm
              form={form}
              layout="vertical"
              requiredMark={true}
              className="form-group-info"
            >
              <div className="form-row">
                <AntForm.Item
                  name="name"
                  label={t("GROUP_NAME")}
                  rules={[RULE.REQUIRED]}
                  className="title-field"
                >
                  <Input placeholder={t("ENTER_GROUP_NAME")}/>
                </AntForm.Item>
              </div>

              <div className="form-row">
                <AntForm.Item
                  name="description"
                  label={t("DESCRIPTION")}
                  className="title-field"
                >
                  <Input.TextArea autoSize={{minRows: 2, maxRows: 4}} placeholder={t("ENTER_DESCRIPTION")}/>
                </AntForm.Item>
              </div>

              <div className="form-row">
                <AntForm.Item
                  name="type"
                  label={t("GROUP_TYPE")}
                  rules={[RULE.REQUIRED]}
                  className="type-field"
                >
                  <Radio.Group
                    options={GROUP_TYPES}
                    onChange={handleGroupTypeChange}
                  />
                </AntForm.Item>

                {groupType === "automatic" && (
                  <AntForm.Item
                    name="automaticType"
                    label={t("AUTOMATIC_TYPE")}
                    rules={[RULE.REQUIRED]}
                    className="type-field"
                  >
                    <Select
                      placeholder={t("SELECT_AUTOMATIC_TYPE")}
                      options={AUTOMATIC_TYPES}
                    />
                  </AntForm.Item>
                )}
              </div>
            </AntForm>
          </div>
        </div>

        {groupType === "manual" && (
          <div className="group-conditions-container">
            <div className="info-header">
              <h3 className="section-title">{t("FILTER_CONDITIONS")}</h3>
            </div>
            <div className="info-content">
              <AntForm
                form={form}
                layout="vertical"
                requiredMark={false}
                className="form-group-conditions"
              >
                <div className="form-row">
                  <AntForm.Item
                    name="registrationDateRange"
                    label={t("REGISTRATION_DATE_RANGE")}
                  >
                    <DatePicker.RangePicker style={{width: '100%'}}/>
                  </AntForm.Item>

                  <AntForm.Item
                    name="paidRegistrationDateRange"
                    label={t("PAID_REGISTRATION_DATE_RANGE")}
                  >
                    <DatePicker.RangePicker style={{width: '100%'}}/>
                  </AntForm.Item>
                </div>

                <div className="form-row">
                  <AntForm.Item
                    name="packageExpirationDateRange"
                    label={t("PACKAGE_EXPIRATION_DATE_RANGE")}
                  >
                    <DatePicker.RangePicker style={{width: '100%'}}/>
                  </AntForm.Item>

                  <AntForm.Item
                    name="responseStatus"
                    label={t("RESPONSE_STATUS")}
                  >
                    <Select
                      placeholder={t("SELECT_RESPONSE_STATUS")}
                      options={RESPONSE_STATUS}
                      allowClear
                    />
                  </AntForm.Item>
                </div>

                <div className="form-row">
                  <AntForm.Item
                    label={t("USAGE_DAYS_RANGE")}
                    className="range-field"
                  >
                    <Row gutter={8}>
                      <Col span={12}>
                        <AntForm.Item
                          name={["usageDaysRange", 0]}
                          noStyle
                        >
                          <InputNumber
                            placeholder={t("MIN_DAYS")}
                            style={{width: '100%'}}
                            min={0}
                            controls={false}
                          />
                        </AntForm.Item>
                      </Col>
                      <Col span={12}>
                        <AntForm.Item
                          name={["usageDaysRange", 1]}
                          noStyle
                        >
                          <InputNumber
                            placeholder={t("MAX_DAYS")}
                            style={{width: '100%'}}
                            min={0}
                            controls={false}
                          />
                        </AntForm.Item>
                      </Col>
                    </Row>
                  </AntForm.Item>

                  <AntForm.Item
                    label={t("INACTIVE_DAYS_RANGE")}
                    className="range-field"
                  >
                    <Row gutter={8}>
                      <Col span={12}>
                        <AntForm.Item
                          name={["inactiveDaysRange", 0]}
                          noStyle
                        >
                          <InputNumber
                            placeholder={t("MIN_DAYS")}
                            style={{width: '100%'}}
                            min={0}
                            controls={false}
                          />
                        </AntForm.Item>
                      </Col>
                      <Col span={12}>
                        <AntForm.Item
                          name={["inactiveDaysRange", 1]}
                          noStyle
                        >
                          <InputNumber
                            placeholder={t("MAX_DAYS")}
                            style={{width: '100%'}}
                            min={0}
                            controls={false}
                          />
                        </AntForm.Item>
                      </Col>
                    </Row>
                  </AntForm.Item>
                </div>

                <div className="form-row">
                  <AntForm.Item
                    name="packageTypes"
                    label={t("PACKAGE_TYPES")}
                  >
                    <Select
                      placeholder={t("SELECT_PACKAGE_TYPES")}
                      options={PACKAGE_TYPES}
                      mode="multiple"
                      allowClear
                    />
                  </AntForm.Item>

                  <AntForm.Item
                    name="usedPromotionCodes"
                    label={t("USED_PROMOTION_CODES")}
                  >
                    <Select
                      placeholder={t("SELECT_PROMOTION_CODES")}
                      options={PROMOTION_CODES}
                      mode="multiple"
                      allowClear
                    />
                  </AntForm.Item>
                </div>
              </AntForm>
            </div>
          </div>
        )}

        <div className="save-button-container">
          <AntButton
            type={BUTTON.LIGHT_NAVY}
            size="large"
            onClick={handleCancel}
            style={{marginRight: '16px'}}
            className="cancel-button"
          >
            {t("CANCEL")}
          </AntButton>
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            onClick={handleSave}
            className="save-button"
            icon={<SaveOutlined/>}
          >
            {isCreating ? t("CREATE") : t("SAVE")}
          </AntButton>
        </div>
      </div>
    </Loading>
  );
};

export default EmailGroupDetail;
