import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Card, Form, Input, Row, Col, Select, Tag, Tooltip } from "antd";
import { SearchOutlined, EditOutlined, PlusOutlined, PlayCircleOutlined, PauseCircleOutlined, Bar<PERSON><PERSON>Outlined, MailOutlined, SendOutlined, CheckOutlined, SwapOutlined, DeleteOutlined } from "@ant-design/icons";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import { LINK } from "@link";
import { BUTTON, PAGINATION_INIT } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, orderColumn, paginationConfig, handleReplaceUrlSearch, formatTimeDate } from "@common/functionCommons";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import "../EmailMarketing.scss";

import { getPaginationCampaigns, deleteCampaign, updateCampaignStatus } from "@services/EmailMarketing";

// Định nghĩa các loại chiến dịch
const CAMPAIGN_TYPES = [
  { value: "automatic", label: t => t("CAMPAIGN_TYPE_AUTOMATED") },
  { value: "manual", label: t => t("CAMPAIGN_TYPE_MANUAL") }
];

// Định nghĩa các trạng thái chiến dịch
const CAMPAIGN_STATUS = [
  { value: "draft", label: t => t("CAMPAIGN_STATUS_DRAFT") },
  { value: "scheduled", label: t => t("CAMPAIGN_STATUS_SCHEDULED") },
  { value: "running", label: t => t("CAMPAIGN_STATUS_ACTIVE") },
  { value: "paused", label: t => t("CAMPAIGN_STATUS_PAUSED") },
  { value: "completed", label: t => t("CAMPAIGN_STATUS_COMPLETED") },
  { value: "failed", label: t => t("CAMPAIGN_STATUS_FAILED") }
];

const EmailCampaign = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [campaignData, setCampaignData] = useState(PAGINATION_INIT);
  const [isLoading, setLoading] = useState(false);

  const [formFilter] = Form.useForm();

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getCampaignData(paging, query);
  }, [location.search]);

  const getCampaignData = async (paging = campaignData.paging, query = campaignData.query) => {
    setLoading(true);
    try {
      const dataResponse = await getPaginationCampaigns(paging, query);
      if (dataResponse) {
        setCampaignData(handlePagingData(dataResponse, query));
      }
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      toast.error(t("ERROR_FETCHING_CAMPAIGNS"));
    } finally {
      setLoading(false);
    }
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, campaignData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, campaignData.paging.pageSize, {});
  };

  const handleDelete = (campaignId, campaignName) => {
    confirm({
      title: t("CONFIRM_DELETE_CAMPAIGN"),
      content: t("CONFIRM_DELETE_CAMPAIGN_CONTENT"),
      onOk: async () => {
        setLoading(true);
        try {
          const apiResponse = await deleteCampaign(campaignId);
          if (apiResponse) {
            toast.success(t("DELETE_CAMPAIGN_SUCCESS"));
            await getCampaignData();
          } else {
            toast.error(t("DELETE_CAMPAIGN_ERROR"));
          }
        } catch (error) {
          console.error("Error deleting campaign:", error);
          toast.error(t("DELETE_CAMPAIGN_ERROR"));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleToggleStatus = async (id, currentStatus) => {
    let newStatus;

    // Xác định trạng thái mới dựa trên trạng thái hiện tại
    if (currentStatus === "running") {
      newStatus = "paused";
    } else if (currentStatus === "paused") {
      newStatus = "running";
    } else if (currentStatus === "scheduled") {
      newStatus = "paused";
    } else {
      return; // Không thay đổi trạng thái cho các trạng thái khác
    }

    setLoading(true);
    try {
      const response = await updateCampaignStatus(id, newStatus);
      if (response) {
        toast.success(t(newStatus === "running" ? "CAMPAIGN_ACTIVATED" : "CAMPAIGN_PAUSED"));
        await getCampaignData();
      } else {
        toast.error(t("UPDATE_CAMPAIGN_STATUS_ERROR"));
      }
    } catch (error) {
      console.error("Error updating campaign status:", error);
      toast.error(t("UPDATE_CAMPAIGN_STATUS_ERROR"));
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      ...orderColumn(campaignData.paging),
      width: 80,
    },
    {
      title: t("CAMPAIGN_NAME"),
      dataIndex: "name",
      key: "name",
      width: 250,
      render: (text, record) => (
        <Link to={LINK.ADMIN.EMAIL_CAMPAIGN_ID.format(record._id)}>
          <span className="name-value">{text}</span>
        </Link>
      ),
    },
    {
      title: t("STATUS"),
      dataIndex: "status",
      key: "status",
      width: 150,
      render: (status) => {
        let color = "default";
        switch (status) {
          case "running":
            color = "success";
            break;
          case "paused":
            color = "warning";
            break;
          case "scheduled":
            color = "processing";
            break;
          case "completed":
            color = "cyan";
            break;
          case "failed":
            color = "error";
            break;
          default:
            color = "default";
        }

        return <Tag color={color}>{t(`CAMPAIGN_STATUS_${status.toUpperCase()}`)}</Tag>;
      },
    },
    {
      title: t("CAMPAIGN_TYPE"),
      dataIndex: "type",
      key: "type",
      width: 150,
      render: (type) => {
        const typeObj = CAMPAIGN_TYPES.find(item => item.value === type) || CAMPAIGN_TYPES[0];
        return typeObj.label(t);
      },
    },
    {
      title: t("TARGET_GROUPS"),
      dataIndex: "targetGroups",
      key: "targetGroups",
      width: 150,
      render: (targetGroups) => {
        if (!targetGroups || targetGroups.length === 0) return <span className="no-data">-</span>;

        // Hàm cắt ngắn tên nhóm
        const truncateGroupName = (name) => {
          if (name && name.length > 20) {
            return name.substring(0, 20) + '...';
          }
          return name;
        };

        // Tạo mảng màu sắc cho các tag
        const colors = ['blue', 'green', 'purple', 'cyan', 'magenta'];

        return (
          <div className="target-groups-container">
            {targetGroups.slice(0, 2).map((group, index) => (
              <Tooltip key={group._id} title={group.name}>
                <Tag
                  color={colors[index % colors.length]}
                  className="target-group-tag"
                >
                  {truncateGroupName(group.name)}
                </Tag>
              </Tooltip>
            ))}
            {targetGroups.length > 2 && (
              <Tooltip
                title={
                  <div className="tooltip-groups">
                    {targetGroups.slice(2).map((group, index) => (
                      <div key={group._id} className="tooltip-group-item">
                        {group.name}
                      </div>
                    ))}
                  </div>
                }
              >
                <Tag color="default" className="more-groups-tag">
                  +{targetGroups.length - 2}
                </Tag>
              </Tooltip>
            )}
          </div>
        );
      },
    },
    {
      title: t("STATISTICS"),
      key: "statistics",
      width: 350,
      render: (_, record) => (
        <div className="campaign-statistics">
          {record.statistics ? (
            <div className="stat-cards">
              <div className="stat-card sent">
                <div className="stat-icon">
                  <SendOutlined />
                </div>
                <div className="stat-content">
                  <div className="stat-value">{record.statistics.totalSent || 0}</div>
                  <div className="stat-label">{t("EMAILS_SENT")}</div>
                </div>
              </div>

              <div className="stat-card open">
                <div className="stat-icon">
                  <MailOutlined />
                </div>
                <div className="stat-content">
                  <div className="stat-value">{record.statistics.openRate || 0}%</div>
                  <div className="stat-label">{t("OPEN_RATE")}</div>
                </div>
              </div>

              <div className="stat-card click">
                <div className="stat-icon">
                  <CheckOutlined />
                </div>
                <div className="stat-content">
                  <div className="stat-value">{record.statistics.clickRate || 0}%</div>
                  <div className="stat-label">{t("CLICK_RATE")}</div>
                </div>
              </div>

              <div className="stat-card conversion">
                <div className="stat-icon">
                  <SwapOutlined />
                </div>
                <div className="stat-content">
                  <div className="stat-value">{record.statistics.conversionRate || 0}%</div>
                  <div className="stat-label">{t("CONVERSION_RATE")}</div>
                </div>
              </div>
            </div>
          ) : (
            <span className="no-stats">{t("NO_STATISTICS_FOUND")}</span>
          )}
        </div>
      ),
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (text) => formatTimeDate(text),
    },
    {
      title: t("ACTIONS"),
      key: "actions",
      width: 180,
      align: "center",
      render: (_, record) => (
        <div className="email-marketing-actions">
          <Tooltip title={t("EDIT")}>
            <Link to={LINK.ADMIN.EMAIL_CAMPAIGN_ID.format(record._id)}>
              <AntButton
                icon={<EditOutlined />}
                className="btn-edit"
                size="small"
              />
            </Link>
          </Tooltip>

          {(record.status === "running" || record.status === "paused" || record.status === "scheduled") && (
            <Tooltip title={record.status === "running" ? t("PAUSE_CAMPAIGN") : t("ACTIVATE_CAMPAIGN")}>
              <AntButton
                icon={record.status === "running" ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={() => handleToggleStatus(record._id, record.status)}
                className={`btn-toggle-status ${record.status === "running" ? "ant-btn-warning" : "ant-btn-success"}`}
                size="small"
              />
            </Tooltip>
          )}

          <Tooltip title={t("VIEW_STATISTICS")}>
            <Link to={`${LINK.ADMIN.EMAIL_STATISTICS}?campaignId=${record._id}`}>
              <AntButton
                type="primary"
                icon={<BarChartOutlined />}
                className="btn-statistics"
                size="small"
              />
            </Link>
          </Tooltip>

          <Tooltip title={t("DELETE")}>
            <AntButton
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record._id, record.name)}
              className="btn-delete"
              size="small"
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(campaignData.paging, campaignData.query, i18n.language);

  return (
    <Loading active={isLoading} transparent>
      <div className="email-marketing-container">
        <Card className="email-marketing-info-card">
          <div className="email-marketing-info-header">
            <div>
              <h1 className="email-marketing-title">{t("EMAIL_CAMPAIGN_MANAGEMENT")}</h1>
              <p className="email-marketing-description">{t("EMAIL_CAMPAIGN_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN.EMAIL_CAMPAIGN_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_CAMPAIGN")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="email-marketing-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_CAMPAIGN_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="status" className="search-form-item">
                      <Select
                        options={CAMPAIGN_STATUS}
                        allowClear
                        placeholder={t("FILTER_BY_STATUS")}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="type" className="search-form-item">
                      <Select
                        options={CAMPAIGN_TYPES}
                        placeholder={t("FILTER_BY_TYPE")}
                        allowClear
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="email-marketing-table-card">
          <TableAdmin
            columns={columns}
            dataSource={campaignData.rows}
            pagination={{ ...pagination }}
            scroll={{ x: 1000 }}
            className={"email-marketing-table"}
            rowClassName={() => "email-marketing-table-row"}
            locale={{ emptyText: t("NO_CAMPAIGNS_FOUND") }}
            rowKey="_id"
          />
        </Card>
      </div>
    </Loading>
  );
};

export default EmailCampaign;
