import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { Card, Form, Row, Col, Select, DatePicker, Statistic, Table, Tabs } from "antd";
import { SearchOutlined, MailOutlined, EyeOutlined, CheckCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { Line, Pie } from '@ant-design/plots';

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import Loading from "@component/Loading";

import { BUTTON } from "@constant";
import { formatTimeDate } from "@common/functionCommons";

import "../EmailMarketing.scss";

// Mô phỏng dữ liệu và API calls
const mockStatisticsData = {
  overview: {
    totalSent: 12500,
    totalOpened: 7800,
    totalClicked: 3200,
    totalUnsubscribed: 150,
    openRate: 62.4,
    clickRate: 25.6,
    unsubscribeRate: 1.2
  },
  campaignPerformance: [
    {
      id: "1",
      name: "<PERSON><PERSON><PERSON> dịch chào mừng",
      sent: 5000,
      opened: 3500,
      clicked: 1800,
      unsubscribed: 50,
      openRate: 70,
      clickRate: 36,
      unsubscribeRate: 1
    },
    {
      id: "2",
      name: "Thông báo khuyến mãi tháng 5",
      sent: 4500,
      opened: 2800,
      clicked: 900,
      unsubscribed: 75,
      openRate: 62.2,
      clickRate: 20,
      unsubscribeRate: 1.7
    },
    {
      id: "3",
      name: "Nhắc nhở gia hạn gói dịch vụ",
      sent: 3000,
      opened: 1500,
      clicked: 500,
      unsubscribed: 25,
      openRate: 50,
      clickRate: 16.7,
      unsubscribeRate: 0.8
    }
  ],
  timeSeriesData: [
    { date: '2025-05-01', sent: 420, opened: 300, clicked: 150 },
    { date: '2025-05-02', sent: 380, opened: 250, clicked: 120 },
    { date: '2025-05-03', sent: 450, opened: 320, clicked: 180 },
    { date: '2025-05-04', sent: 500, opened: 380, clicked: 200 },
    { date: '2025-05-05', sent: 480, opened: 350, clicked: 190 },
    { date: '2025-05-06', sent: 520, opened: 400, clicked: 210 },
    { date: '2025-05-07', sent: 580, opened: 420, clicked: 230 },
    { date: '2025-05-08', sent: 600, opened: 450, clicked: 250 },
    { date: '2025-05-09', sent: 550, opened: 400, clicked: 220 },
    { date: '2025-05-10', sent: 590, opened: 430, clicked: 240 },
    { date: '2025-05-11', sent: 620, opened: 470, clicked: 260 },
    { date: '2025-05-12', sent: 580, opened: 420, clicked: 230 },
    { date: '2025-05-13', sent: 600, opened: 450, clicked: 240 },
    { date: '2025-05-14', sent: 650, opened: 500, clicked: 270 },
    { date: '2025-05-15', sent: 700, opened: 520, clicked: 290 },
  ],
  deviceData: [
    { type: 'Desktop', value: 45 },
    { type: 'Mobile', value: 40 },
    { type: 'Tablet', value: 15 },
  ],
  recentActivity: [
    {
      id: "1",
      campaign: "Chiến dịch chào mừng",
      email: "<EMAIL>",
      action: "opened",
      timestamp: "2025-05-15T10:30:00.000Z"
    },
    {
      id: "2",
      campaign: "Thông báo khuyến mãi tháng 5",
      email: "<EMAIL>",
      action: "clicked",
      timestamp: "2025-05-15T10:25:00.000Z"
    },
    {
      id: "3",
      campaign: "Nhắc nhở gia hạn gói dịch vụ",
      email: "<EMAIL>",
      action: "unsubscribed",
      timestamp: "2025-05-15T10:20:00.000Z"
    },
    {
      id: "4",
      campaign: "Chiến dịch chào mừng",
      email: "<EMAIL>",
      action: "opened",
      timestamp: "2025-05-15T10:15:00.000Z"
    },
    {
      id: "5",
      campaign: "Thông báo khuyến mãi tháng 5",
      email: "<EMAIL>",
      action: "clicked",
      timestamp: "2025-05-15T10:10:00.000Z"
    }
  ]
};

// Mô phỏng API calls
const getStatisticsData = async (filters) => {
  console.log("Fetching statistics with filters:", filters);
  // Mô phỏng API call
  return mockStatisticsData;
};

// Định nghĩa các chiến dịch
const CAMPAIGNS = [
  { value: "all", label: "Tất cả chiến dịch" },
  { value: "1", label: "Chiến dịch chào mừng" },
  { value: "2", label: "Thông báo khuyến mãi tháng 5" },
  { value: "3", label: "Nhắc nhở gia hạn gói dịch vụ" }
];

// Định nghĩa các khoảng thời gian
const TIME_RANGES = [
  { value: "7days", label: "7 ngày qua" },
  { value: "30days", label: "30 ngày qua" },
  { value: "90days", label: "90 ngày qua" },
  { value: "custom", label: "Tùy chỉnh" }
];

const EmailStatistics = () => {
  const { t } = useTranslation();
  const location = useLocation();

  const [statisticsData, setStatisticsData] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState("30days");
  const [showDatePicker, setShowDatePicker] = useState(false);

  const [formFilter] = Form.useForm();

  useEffect(() => {
    getStatisticsDataWithFilters();
  }, []);

  const getStatisticsDataWithFilters = async () => {
    setLoading(true);
    const filters = formFilter.getFieldsValue();
    const dataResponse = await getStatisticsData(filters);
    if (dataResponse) {
      setStatisticsData(dataResponse);
    }
    setLoading(false);
  };

  const onSubmitFilter = () => {
    getStatisticsDataWithFilters();
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    formFilter.setFieldsValue({
      campaign: "all",
      timeRange: "30days"
    });
    setTimeRange("30days");
    setShowDatePicker(false);
    getStatisticsDataWithFilters();
  };

  const handleTimeRangeChange = (value) => {
    setTimeRange(value);
    setShowDatePicker(value === "custom");
  };

  // Cấu hình biểu đồ đường
  const lineConfig = {
    data: statisticsData?.timeSeriesData || [],
    xField: 'date',
    yField: 'value',
    seriesField: 'category',
    yAxis: {
      label: {
        formatter: (v) => `${v}`,
      },
    },
    legend: {
      position: 'top',
    },
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  // Chuẩn bị dữ liệu cho biểu đồ đường
  const lineData = [];
  if (statisticsData?.timeSeriesData) {
    statisticsData.timeSeriesData.forEach(item => {
      lineData.push({ date: item.date, value: item.sent, category: 'Đã gửi' });
      lineData.push({ date: item.date, value: item.opened, category: 'Đã mở' });
      lineData.push({ date: item.date, value: item.clicked, category: 'Đã nhấp' });
    });
  }

  // Cấu hình biểu đồ tròn
  const pieConfig = {
    data: statisticsData?.deviceData || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [
      {
        type: 'pie-legend-active',
      },
      {
        type: 'element-active',
      },
    ],
  };

  // Cột cho bảng hiệu suất chiến dịch
  const campaignColumns = [
    {
      title: t("CAMPAIGN_NAME"),
      dataIndex: "name",
      key: "name",
      width: 200,
    },
    {
      title: t("SENT"),
      dataIndex: "sent",
      key: "sent",
      width: 100,
      sorter: (a, b) => a.sent - b.sent,
    },
    {
      title: t("OPENED"),
      dataIndex: "opened",
      key: "opened",
      width: 100,
      sorter: (a, b) => a.opened - b.opened,
    },
    {
      title: t("CLICKED"),
      dataIndex: "clicked",
      key: "clicked",
      width: 100,
      sorter: (a, b) => a.clicked - b.clicked,
    },
    {
      title: t("OPEN_RATE"),
      dataIndex: "openRate",
      key: "openRate",
      width: 120,
      render: (text) => `${text}%`,
      sorter: (a, b) => a.openRate - b.openRate,
    },
    {
      title: t("CLICK_RATE"),
      dataIndex: "clickRate",
      key: "clickRate",
      width: 120,
      render: (text) => `${text}%`,
      sorter: (a, b) => a.clickRate - b.clickRate,
    },
    {
      title: t("UNSUBSCRIBE_RATE"),
      dataIndex: "unsubscribeRate",
      key: "unsubscribeRate",
      width: 150,
      render: (text) => `${text}%`,
      sorter: (a, b) => a.unsubscribeRate - b.unsubscribeRate,
    },
  ];

  // Cột cho bảng hoạt động gần đây
  const activityColumns = [
    {
      title: t("CAMPAIGN"),
      dataIndex: "campaign",
      key: "campaign",
      width: 200,
    },
    {
      title: t("EMAIL"),
      dataIndex: "email",
      key: "email",
      width: 200,
    },
    {
      title: t("ACTION"),
      dataIndex: "action",
      key: "action",
      width: 150,
      render: (text) => {
        let icon;
        let color;
        let label;
        
        switch (text) {
          case "opened":
            icon = <EyeOutlined />;
            color = "blue";
            label = t("OPENED");
            break;
          case "clicked":
            icon = <CheckCircleOutlined />;
            color = "green";
            label = t("CLICKED");
            break;
          case "unsubscribed":
            icon = <CloseCircleOutlined />;
            color = "red";
            label = t("UNSUBSCRIBED");
            break;
          default:
            icon = <MailOutlined />;
            color = "default";
            label = text;
        }
        
        return (
          <span style={{ color }}>
            {icon} {label}
          </span>
        );
      },
    },
    {
      title: t("TIMESTAMP"),
      dataIndex: "timestamp",
      key: "timestamp",
      width: 150,
      render: (text) => formatTimeDate(text),
    },
  ];

  return (
    <Loading active={isLoading} transparent>
      <div className="email-marketing-container">
        <Card className="email-marketing-info-card">
          <div className="email-marketing-info-header">
            <div>
              <h1 className="email-marketing-title">{t("EMAIL_STATISTICS")}</h1>
              <p className="email-marketing-description">{t("EMAIL_STATISTICS_DESCRIPTION")}</p>
            </div>
          </div>
        </Card>

        <Card className="email-marketing-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="campaign" className="search-form-item" initialValue="all">
                      <Select
                        options={CAMPAIGNS}
                        placeholder={t("SELECT_CAMPAIGN")}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="timeRange" className="search-form-item" initialValue="30days">
                      <Select
                        options={TIME_RANGES}
                        placeholder={t("SELECT_TIME_RANGE")}
                        onChange={handleTimeRangeChange}
                      />
                    </AntForm.Item>
                  </Col>
                  {showDatePicker && (
                    <Col xs={24} md={12} lg={8}>
                      <AntForm.Item name="dateRange" className="search-form-item">
                        <DatePicker.RangePicker style={{ width: '100%' }} />
                      </AntForm.Item>
                    </Col>
                  )}
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("RESET")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("APPLY")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        {statisticsData && (
          <>
            <Card className="email-marketing-info-card">
              <Row gutter={24}>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title={t("TOTAL_SENT")}
                    value={statisticsData.overview.totalSent}
                    prefix={<MailOutlined />}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title={t("TOTAL_OPENED")}
                    value={statisticsData.overview.totalOpened}
                    prefix={<EyeOutlined />}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title={t("OPEN_RATE")}
                    value={statisticsData.overview.openRate}
                    suffix="%"
                    precision={1}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title={t("CLICK_RATE")}
                    value={statisticsData.overview.clickRate}
                    suffix="%"
                    precision={1}
                  />
                </Col>
              </Row>
            </Card>

            <Tabs defaultActiveKey="1">
              <Tabs.TabPane tab={t("PERFORMANCE_OVER_TIME")} key="1">
                <Card className="email-marketing-table-card">
                  <h3>{t("EMAIL_PERFORMANCE_OVER_TIME")}</h3>
                  <Line {...lineConfig} data={lineData} />
                </Card>
              </Tabs.TabPane>
              
              <Tabs.TabPane tab={t("CAMPAIGN_PERFORMANCE")} key="2">
                <Card className="email-marketing-table-card">
                  <h3>{t("CAMPAIGN_PERFORMANCE")}</h3>
                  <Table
                    columns={campaignColumns}
                    dataSource={statisticsData.campaignPerformance}
                    rowKey="id"
                    pagination={false}
                    scroll={{ x: 1000 }}
                  />
                </Card>
              </Tabs.TabPane>
              
              <Tabs.TabPane tab={t("DEVICE_BREAKDOWN")} key="3">
                <Card className="email-marketing-table-card">
                  <h3>{t("DEVICE_BREAKDOWN")}</h3>
                  <Pie {...pieConfig} />
                </Card>
              </Tabs.TabPane>
              
              <Tabs.TabPane tab={t("RECENT_ACTIVITY")} key="4">
                <Card className="email-marketing-table-card">
                  <h3>{t("RECENT_ACTIVITY")}</h3>
                  <Table
                    columns={activityColumns}
                    dataSource={statisticsData.recentActivity}
                    rowKey="id"
                    pagination={false}
                    scroll={{ x: 1000 }}
                  />
                </Card>
              </Tabs.TabPane>
            </Tabs>
          </>
        )}
      </div>
    </Loading>
  );
};

export default EmailStatistics;
