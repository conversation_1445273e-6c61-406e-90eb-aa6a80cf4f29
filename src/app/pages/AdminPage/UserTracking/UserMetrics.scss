// User Metrics styling
.user-metrics-summary-card {
  border-radius: 8px;
  box-shadow: var(--shadow-level-1);
  transition: all 0.3s ease;
  margin-bottom: 0;

  &:hover {
    box-shadow: var(--shadow-level-2);
  }

  .user-metrics-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 24px;
    color: var(--typo-colours-primary-black, #000);
  }

  .user-metrics-summary-row {
    margin-bottom: 16px;
  }

  // Stat cards styling
  .user-metrics-stat-card {
    height: 100%;
    border-radius: 12px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;
    padding: 16px;
    position: relative;
    overflow: hidden;

    &:hover {
      box-shadow: var(--shadow-level-2);
      transform: translateY(-4px);
    }

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
    }

    &.daily-card:before {
      background-color: #1890ff; // Blue
    }

    &.monthly-card:before {
      background-color: #52c41a; // Green
    }

    &.retention-card:before {
      background-color: #722ed1; // Purple
    }

    .ant-statistic-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      margin-bottom: 12px;
      font-weight: 600;
      color: var(--typo-colours-primary-black, #000);
    }

    .ant-statistic-content {
      font-size: 28px;
      font-weight: 700;
      color: var(--typo-colours-primary-black, #000);
    }

    .info-icon {
      position: absolute;
      top: 16px;
      right: 16px;
      color: rgba(0, 0, 0, 0.45);
      cursor: pointer;
      font-size: 16px;

      &:hover {
        color: rgba(0, 0, 0, 0.65);
      }
    }

    .stat-description {
      margin-top: 8px;
      font-size: 14px;
      color: var(--typo-colours-secondary-grey, rgba(0, 0, 0, 0.45));
      margin-bottom: 16px;
    }

    .card-extra-info {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      display: flex;
      flex-direction: column;
      gap: 8px;
      font-size: 12px;

      .card-info-item {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 4px;
        color: var(--typo-colours-secondary-grey, rgba(0, 0, 0, 0.45));

        .info-item-icon {
          font-size: 12px;
        }

        .info-item-label {
          font-weight: 500;
        }

        .info-item-value {
          color: var(--typo-colours-primary-black, rgba(0, 0, 0, 0.65));
          word-break: break-all;
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .ant-col {
      margin-bottom: 16px;
    }

    .user-metrics-stat-card {
      .ant-statistic-content {
        font-size: 24px;
      }
    }
  }
}
