import {useEffect, useState, useMemo} from "react";
import {useLocation} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {Card, Typography} from "antd";
import {UserOutlined} from "@ant-design/icons";

import AccessFrequency from "./AccessFrequency";
import Filter from "./Filter";
import UserMetrics from "./UserMetrics";
import Loading from "@src/app/component/Loading";

import {getTracking} from "@services/Tracking";
import {convertQueryToObject, cloneObj} from "@common/functionCommons";
import {handlePagingData} from "@src/common/dataConverter";
import {PAGINATION_INIT} from "@constant";

import "./UserTracking.scss";

const {Title, Paragraph} = Typography;

function UserTracking() {
  const {t} = useTranslation();
  const location = useLocation();
  const [trackingData, setTrackingData] = useState(PAGINATION_INIT);
  const [metricsData, setMetricsData] = useState({
    mau: 0,
    dau: 0,
  });
  const [isLoading, setIsLoading] = useState(false);

  // get query object from url
  const queryParams = useMemo(() => convertQueryToObject(location.search), [location.search]);

  useEffect(() => {
    getTrackingData(queryParams);
  }, [location.search]);

  const getTrackingData = async (queryParams) => {
    let newQuery = cloneObj(queryParams);
    newQuery.page ||= PAGINATION_INIT.paging.page;
    newQuery.limit ||= PAGINATION_INIT.paging.pageSize;
    if (newQuery.isDeveloper) newQuery.isDeveloper = JSON.parse(newQuery.isDeveloper);
    setIsLoading(true);
    const response = await getTracking(newQuery);
    if (response) {
      setTrackingData(handlePagingData(response.users));
      setMetricsData({
        dau: response.dau,
        mau: response.mau
      });
    }
    setIsLoading(false);
  };

  return (
    <div className="user-tracking-container">
      <div className="user-tracking-content">
        <Loading active={isLoading} transparent>
          <div className="user-tracking-card-wrapper">
            <Card className="user-tracking-info-card">
              <div className="user-tracking-info-header">
                <div>
                  <h2 className="user-tracking-title">{t("USER_TRACKING")}</h2>
                  <p className="user-tracking-description">{t("USER_TRACKING_DESCRIPTION")}</p>
                </div>
                <UserOutlined className="user-tracking-icon" style={{fontSize: '32px', color: '#1890ff'}} />
              </div>
            </Card>
          </div>
          <div className="user-tracking-card-wrapper">
            <Filter/>
          </div>
          <div className="user-tracking-card-wrapper">
            <UserMetrics metricsData={metricsData} queryParams={queryParams}/>
          </div>
          <div className="user-tracking-card-wrapper">
            <AccessFrequency
              trackingData={trackingData}
              setTrackingData={setTrackingData}
              queryParams={queryParams}
              isLoading={isLoading}
              setIsLoading={setIsLoading}
            />
          </div>
        </Loading>
      </div>
    </div>
  );
}

export default UserTracking;
