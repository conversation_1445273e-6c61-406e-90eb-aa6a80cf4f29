import { useMemo } from "react";
import { Card, Col, Row, Statistic, Tooltip, Typography, Empty, Divider } from "antd";
import { useTranslation } from "react-i18next";
import {
  UserOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  CalendarOutlined,
  RiseOutlined,
  GlobalOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";

import Loading from "@src/app/component/Loading";

import "./UserMetrics.scss";

const { Text } = Typography;

const UserMetrics = ({ metricsData, queryParams }) => {
  const { t } = useTranslation();

  // Xác định kho<PERSON>ng thời gian
  const getDateRangeText = () => {
    const { time, fromDate, toDate } = queryParams || {};

    if (time === "week" || time === "month") {
      const startDate = time === "week"
        ? dayjs().startOf("week").format("DD/MM/YYYY")
        : dayjs().startOf("month").format("DD/MM/YYYY");
      const endDate = dayjs().format("DD/MM/YYYY");
      return t("DATE_RANGE_FORMAT").replace("{0}", startDate).replace("{1}", endDate);
    } else if (fromDate && toDate) {
      const startDate = dayjs(fromDate * 1000).format("DD/MM/YYYY");
      const endDate = dayjs(toDate * 1000).format("DD/MM/YYYY");
      return t("DATE_RANGE_FORMAT").replace("{0}", startDate).replace("{1}", endDate);
    }

    return "";
  };

  // Lấy domain hiện tại
  const getDomain = () => {
    // Nếu là môi trường local, trả về domain mặc định
    if (window.location.hostname === "localhost") {
      return t("DEFAULT_DOMAIN");
    }
    return window.location.origin;
  };

  return (
    <Card className="user-metrics-summary-card">
      {metricsData.dau === 0 && metricsData.mau === 0 ? (
          <Empty description={t("NO_USER_DATA_FOUND")} />
        ) : (
          <>
            <h2 className="user-metrics-title">{t("USER_ACTIVITY_OVERVIEW")}</h2>
            <Row gutter={[24, 24]} className="user-metrics-summary-row">
              <Col xs={24} sm={12} md={8}>
                <Card className="user-metrics-stat-card daily-card">
                  <Statistic
                    title={t("DAILY_ACTIVE_USERS")}
                    value={metricsData.dau.toFixed(1)}
                    prefix={<UserOutlined />}
                  />
                  <Tooltip title={t("AVERAGE_DAILY_ACTIVE_USERS_DESC")}>
                    <InfoCircleOutlined className="info-icon" />
                  </Tooltip>
                  <div className="stat-description">{t("AVERAGE_USERS_PER_DAY")}</div>
                  <div className="card-extra-info">
                    {getDateRangeText() && (
                      <div className="card-info-item">
                        <CalendarOutlined className="info-item-icon" />
                        <span className="info-item-label">{t("DATE_RANGE")}:</span>
                        <span className="info-item-value">{getDateRangeText()}</span>
                      </div>
                    )}
                    <div className="card-info-item">
                      <GlobalOutlined className="info-item-icon" />
                      <span className="info-item-label">{t("DOMAIN_INFO")}:</span>
                      <span className="info-item-value">{getDomain()}</span>
                    </div>
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card className="user-metrics-stat-card monthly-card">
                  <Statistic
                    title={t("MONTHLY_ACTIVE_USERS")}
                    value={metricsData.mau}
                    prefix={<TeamOutlined />}
                  />
                  <Tooltip title={t("MONTHLY_ACTIVE_USERS_DESC")}>
                    <InfoCircleOutlined className="info-icon" />
                  </Tooltip>
                  <div className="stat-description">{t("TOTAL_USERS_THIS_MONTH")}</div>
                  <div className="card-extra-info">
                    {getDateRangeText() && (
                      <div className="card-info-item">
                        <CalendarOutlined className="info-item-icon" />
                        <span className="info-item-label">{t("DATE_RANGE")}:</span>
                        <span className="info-item-value">{getDateRangeText()}</span>
                      </div>
                    )}
                    <div className="card-info-item">
                      <GlobalOutlined className="info-item-icon" />
                      <span className="info-item-label">{t("DOMAIN_INFO")}:</span>
                      <span className="info-item-value">{getDomain()}</span>
                    </div>
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card className="user-metrics-stat-card retention-card">
                  <Statistic
                    title={t("USER_RETENTION_RATE")}
                    value={metricsData.mau > 0 ? ((metricsData.dau / metricsData.mau) * 100).toFixed(1) : '0.0'}
                    suffix="%"
                    prefix={<RiseOutlined />}
                  />
                  <Tooltip title={t("USER_RETENTION_RATE_DESC")}>
                    <InfoCircleOutlined className="info-icon" />
                  </Tooltip>
                  <div className="stat-description">{t("DAILY_TO_MONTHLY_RATIO")}</div>
                  <div className="card-extra-info">
                    {getDateRangeText() && (
                      <div className="card-info-item">
                        <CalendarOutlined className="info-item-icon" />
                        <span className="info-item-label">{t("DATE_RANGE")}:</span>
                        <span className="info-item-value">{getDateRangeText()}</span>
                      </div>
                    )}
                    <div className="card-info-item">
                      <GlobalOutlined className="info-item-icon" />
                      <span className="info-item-label">{t("DOMAIN_INFO")}:</span>
                      <span className="info-item-value">{getDomain()}</span>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
          </>
        )}
    </Card>
  );
};

export default UserMetrics;
