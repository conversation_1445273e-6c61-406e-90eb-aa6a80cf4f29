import React, { useEffect, useState } from "react";
import { Col, Form, Input, Row, Select } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import { useNavigate, useParams } from "react-router-dom";

import AntButton from "@component/AntButton";
import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";

import Loading from "@src/app/component/Loading";

import { cloneObj, coverLangArrayToObject, coverLanguageObjectToArray } from "@common/functionCommons";
import { getAllKnowledge } from "@services/Knowledge";
import { createOption, updateOption, getOptionById } from "@services/Option";

import { BUTTON, getTrueFalseOptions, INPUT_TYPE, LANG_OPTIONS, RULES } from "@constant";

import "./OptionDetail.scss";
import { toast } from "@src/app/component/ToastProvider";
import { LINK } from "@link";

const OptionDetail = ({ ...props }) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const [formOption] = Form.useForm();
  const [isSelectType, setSelectType] = useState(false);
  const [nameLanguage, setNameLanguage] = useState([]);
  const [knowledgeData, setKnowledgeData] = useState([]);
  const [optionData, setOptionData] = useState();
  const [isLoading, setLoading] = useState(true);

  useEffect(() => {
    getAllData();
  }, [id]);

  useEffect(() => {
    if (optionData) {
      if (["select", "select_multiple"].includes(optionData.type)) {
        setSelectType(true);
      }
      let repareLocalization = coverLanguageObjectToArray(optionData?.localization);
      delete repareLocalization?.lang;
      formOption.setFieldsValue({ ...optionData, localization: repareLocalization });
    } else setSelectType(false);
  }, [optionData]);

  const getAllData = async () => {
    setLoading(true);
    let allRequest = [getAllKnowledge()];
    if (id) {
      allRequest.push(getOptionById(id));
    }
    const [knowledgeResponse, optionResponse] = await Promise.all(allRequest);
    if (knowledgeResponse) {
      setKnowledgeData(knowledgeResponse);
    }
    if (optionResponse) {
      setOptionData(optionResponse);
    }
    setLoading(false);
  }

  const onFinish = async (values) => {
    const { selectOptions, type, localization, rule, ...rest } = values;
    const dataRequest = {
      ...rest,
      type,
      rule: !!rule ? rule : null,
      ...(optionData && { _id: id }),
      selectOptions: ["select", "select_multiple"].includes(type) ? selectOptions : [],
      localization: coverLangArrayToObject(localization),
    };

    setLoading(true);
    const dataResponse = id ? await updateOption(dataRequest, true) : await createOption(dataRequest, true);

    if (dataResponse) {
      if (!id) {
        navigate(LINK.ADMIN_OPTIONS_DETAIL.format(dataResponse._id));
      }
      toast.success(id ? "UPDATE_OPTION_SUCCESS" : "CREATE_OPTION_SUCCESS");
    }

    setLoading(false);
  };

  const handleCancel = () => {
    navigate(-1);
  };

  const onChangeType = (value) => setSelectType(["select", "select_multiple"].includes(value));

  const getAvailabeOptions = (index, options) => {
    const optionsSelected = options.filter((lang, optionIndex) => optionIndex !== index);
    return LANG_OPTIONS.filter((option) => !optionsSelected.some((selected) => selected.value === option.value));
  };

  const onRemove = (index, name, remove, setData) => {
    remove(index, name);
    setData(prevData => prevData.filter((_, i) => i !== index));
  };

  const onSelect = (data, index, setData) => {
    const { value } = data;
    setData((pre) => {
      const newData = cloneObj(pre);
      newData[index] = value;
      return newData;
    });
  };


  const renderFormItem = (restField, name, label, rules, placeholder, component) => (
    <Form.Item
      {...restField}
      name={name}
      label={label}
      rules={rules}
    >
      {component}
    </Form.Item>
  );

  const renderSelect = (index, name, restField, setNameLanguage) => (
    renderFormItem(
      restField,
      [name, "lang"],
      index === 0 ? "Language" : "",
      [{ required: true, message: "Missing language of name!" }],
      "Select language",
      <Select
        options={getAvailabeOptions(index, nameLanguage)}
        placeholder="Select language"
        onSelect={(_, option) => onSelect(option, index, setNameLanguage)}
      />,
    )
  );

  const renderInput = (index, name, restField) => (
    renderFormItem(
      restField,
      [name, "name"],
      index === 0 ? "Name" : "",
      [{ required: true, message: "Missing name!" }],
      "Enter name",
      <Input placeholder="Enter name" />,
    )
  );


  return (
    <Loading active={isLoading}>
      <div className="option-detail">
        <div className="option-detail__title">{id ? t("INSTRUCTION_OPTION_DETAIL") : t("CREATE_INSTRUCTION_OPTION")}</div>
        <Form id="option-form" onFinish={onFinish} layout="vertical" size={"large"} form={formOption}>
          <Row gutter={24}>
            <Col xs={24} lg={12}>
              <Form.Item label="Name" name="name">
                <Input placeholder={"Enter name"} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item label="Code" name="code" rules={[{ required: true, message: "Code can't be blank!" }]}>
                <Input placeholder={"Enter code"} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item label="Placeholder" name="placeholder">
                <Input placeholder={"Enter placeholder"} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item label="Default value" name="defaultValue">
                <Input placeholder={"Enter default value"} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={24}>
              <Form.Item label="Knowledge" name="knowledgeIds">
                <Select
                  options={knowledgeData}
                  mode="multiple"
                  placeholder={"Select knowledge data"}
                  allowClear
                  fieldNames={{ label: "name", value: "_id" }}
                  optionFilterProp="name"
                />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item label="Type" name="type" rules={[{ required: true, message: "Type can't be blank!" }]}>
                <Select options={INPUT_TYPE} onChange={onChangeType} placeholder={"Select type"} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item label="Rule" name="rule">
                <Select options={RULES} allowClear placeholder={"Select rules"} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name="isExamOption"
                label='Is exam option'
                initialValue={false}
              >
                <Select
                  options={getTrueFalseOptions()}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item label="Instruction" name="instruction">
                <Input.TextArea
                  autoSize={{
                    minRows: 1,
                  }}
                  placeholder={"Input instruction"}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item required className="localization">
            <Form.List name="localization">
              {(fields, { add, remove }) => {
                if (fields.length === 0) add();
                return (
                  <div className={"instruction-localization-container"}>
                    {fields.map(({ key, name, ...restField }, index) => (
                      <div key={index}>
                        <div className={"instruction-localization-item"}>

                          <Row gutter={24} key={key} className="items-baseline">
                            <Col xs={24} lg={6}>
                              {renderSelect(index, name, restField, setNameLanguage)}
                            </Col>
                            <Col xs={24} lg={18}>
                              {renderInput(index, name, restField)}
                            </Col>
                          </Row>

                          <div className={"select-options-item__btnRemove"}>
                            <AntButton
                              type={BUTTON.WHITE}
                              shape={"circle"}
                              className={clsx("btn-cancel-add-options-detail", { "first-actions": !index })}
                              icon={<CancelIcon />}
                              size={"small"}
                              onClick={() => onRemove(index, name, remove, setNameLanguage)}
                            />
                          </div>
                        </div>
                      </div>
                    ))}

                    {fields.length < 2 && (
                      <div className={"add-options-actions"}>
                        <AntButton
                          shape={"circle"}
                          size={"large"}
                          type={BUTTON.WHITE_BLUE}
                          onClick={() => add()}
                          icon={<PlusIcon />}
                        ></AntButton>
                      </div>
                    )}
                  </div>
                );
              }}
            </Form.List>
          </Form.Item>
          {isSelectType && (
            <Form.List name="selectOptions" initialValue={[{ label: null }]}>
              {(fields, { add, remove }) => {
                if (fields.length === 0) add();
                return (<>
                  <div className="select-options-item">
                    <label>Select options:</label>
                  </div>
                  {fields.map(({ key, name, ...restField }, index) => (
                    <div className="select-options-item" key={key}>
                      <Row gutter={24}>
                        <Col xs={24} lg={8}>
                          <Form.Item
                            {...restField}
                            name={[name, "label", "vi"]}
                            rules={[
                              {
                                required: true,
                                message: "Vietnamese label can't be blank!",
                              },
                            ]}
                          >
                            <Input placeholder="Vietnamese label" />
                          </Form.Item>
                        </Col>
                        <Col xs={24} lg={8}>
                          <Form.Item
                            {...restField}
                            name={[name, "label", "en"]}
                            rules={[
                              {
                                required: true,
                                message: "English label can't be blank!",
                              },
                            ]}
                          >
                            <Input placeholder="English label " />
                          </Form.Item>
                        </Col>
                        <Col xs={24} lg={8}>
                          <Form.Item
                            {...restField}
                            name={[name, "value"]}
                            rules={[
                              {
                                required: true,
                                message: "Value can't be blank!",
                              },
                            ]}
                          >
                            <Input placeholder="Value" />
                          </Form.Item>
                        </Col>
                      </Row>
                      <div className={"select-options-item__btnRemove"}>
                        <AntButton
                          type={BUTTON.WHITE}
                          shape={"circle"}
                          className={"btn-cancel-add-options"}
                          icon={<CancelIcon />}
                          size={"small"}
                          onClick={() => remove(name)}
                        />
                      </div>
                    </div>
                  ))}

                  <div className={"add-options-actions"}>
                    <AntButton
                      shape={"circle"}
                      size={"large"}
                      type={BUTTON.WHITE_BLUE}
                      onClick={() => add()}
                      icon={<PlusIcon />}
                    ></AntButton>
                  </div>
                </>
                )
              }
              }
            </Form.List>
          )}
          <Row gutter={20} justify="center" className="gap-4">
            <Form.Item className="save-button">
              <AntButton onClick={handleCancel} type={BUTTON.WHITE}>
                {t("BACK")}
              </AntButton>
            </Form.Item>
            <Form.Item className="save-button">
              <AntButton htmlType="submit" type={BUTTON.DEEP_NAVY}>
                {t("SAVE")}
              </AntButton>
            </Form.Item>
          </Row>
        </Form>
      </div >
    </Loading>
  );
};

export default OptionDetail;
