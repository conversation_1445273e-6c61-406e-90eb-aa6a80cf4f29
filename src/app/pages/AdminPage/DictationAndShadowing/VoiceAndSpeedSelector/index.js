import React, { useEffect, useRef, useState } from "react";
import { Space } from "antd";
import AntButton from "@component/AntButton";
import PauseCircle24 from "@component/SvgIcons/Pause/PauseCircle24";
import PlayAround24 from "@component/SvgIcons/Play/PlayAround24";
import BOY from "@src/asset/voiceOptions/boy.svg";
import ONYX_AUDIO from "@src/asset/voiceAudio/onyx.mp3";
import { BUTTON } from "@constant";
import GIRL from "@src/asset/voiceOptions/girl.svg";
import ALLOY_AUDIO from "@src/asset/voiceAudio/alloy.mp3";
import ECHO_AUDIO from "@src/asset/voiceAudio/echo.mp3";
import NOVA_AUDIO from "@src/asset/voiceAudio/nova.mp3";
import FABLE_AUDIO from "@src/asset/voiceAudio/fable.mp3";
import SHIMMER_AUDIO from "@src/asset/voiceAudio/shimmer.mp3";
import { useTranslation } from "react-i18next";

const VOICE_OPTIONS = {
  ONYX: {
    title: "Onyx",
    value: "onyx",
    avatar: BOY,
    audio: ONYX_AUDIO,
    type: BUTTON.LIGHT_NAVY,
  },
  ALLOY: {
    title: "Alloy",
    value: "alloy",
    avatar: GIRL,
    audio: ALLOY_AUDIO,
    type: BUTTON.LIGHT_PINK,
  },
  ECHO: {
    title: "Echo",
    value: "echo",
    avatar: BOY,
    audio: ECHO_AUDIO,
    type: BUTTON.LIGHT_NAVY,
  },
  NOVA: {
    title: "Nova",
    value: "nova",
    avatar: GIRL,
    audio: NOVA_AUDIO,
    type: BUTTON.LIGHT_PINK,
  },
  FABLE: {
    title: "Fable",
    value: "fable",
    avatar: BOY,
    audio: FABLE_AUDIO,
    type: BUTTON.LIGHT_NAVY,
  },
  SHIMMER: {
    title: "Shimmer",
    value: "shimmer",
    avatar: GIRL,
    audio: SHIMMER_AUDIO,
    type: BUTTON.LIGHT_PINK,
  },
};

const SPEED_OPTIONS = {
  SLOW: {
    lang: "SLOW",
    speedText: "0.5x",
    value: 0.5,
  },
  SLIGHTLY_SLOW: {
    lang: "SLIGHTLY_SLOW",
    speedText: "0.75x",
    value: 0.75,
  },
  NORMAL: {
    lang: "NORMAL",
    speedText: "1x",
    value: 1,
  },
  SLIGHTLY_FAST: {
    lang: "SLIGHTLY_FAST",
    speedText: "1.25x",
    value: 1.25,
  },
  FAST: {
    lang: "FAST",
    speedText: "1.5x",
    value: 1.5,
  },
};

const VoiceAndSpeedSelector = ({ onVoiceChange, onSpeedChange }) => {
  
  const { t } = useTranslation();
  const [selectedVoice, setSelectedVoice] = useState("alloy");
  const [selectedSpeed, setSelectedSpeed] = useState(1); // Mặc định tốc độ 1x
  const [voicePlaying, setVoicePlaying] = useState(null);
  
  const audioRef = useRef(null);
  
  useEffect(() => {
    if (onVoiceChange) {
      onVoiceChange(selectedVoice);
    }
  }, [selectedVoice, onVoiceChange]);
  
  useEffect(() => {
    if (onSpeedChange) {
      onSpeedChange(selectedSpeed);
    }
  }, [selectedSpeed, onSpeedChange]);
  
  
  const handleVoiceSelect = (voiceName) => {
    setSelectedVoice(voiceName);
  };
  
  const handleSpeedSelect = (speed) => {
    setSelectedSpeed(speed);
  };
  
  useEffect(() => {
    audioRef.current?.pause();
    if (voicePlaying) {
      const audio = new Audio(VOICE_OPTIONS[voicePlaying.toUpperCase()].audio);
      audioRef.current = audio;
      audioRef.current.playbackRate = selectedSpeed;
      
      
      audio.addEventListener("ended", () => {
        setVoicePlaying();
      });
      
      audioRef.current.play();
    }
  }, [voicePlaying]);
  
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.playbackRate = selectedSpeed;
    }
  }, [selectedSpeed]);
  
  return (
    <div>
      <div className="voice-speed-container">
        <Space direction="horizontal" size="middle" style={{ marginTop: 8 }}>
          {Object.values(VOICE_OPTIONS).map(({ value, type, avatar, title }) => {
            const isActive = selectedVoice === value;
            const isPlaying = voicePlaying === value;
            
            return (
              <AntButton
                key={value}
                size="large"
                type={type}
                className="option-voice-btn"
                onClick={() => handleVoiceSelect(value)}
                active={isActive}
              >
                <div className="option-voice-avatar">
                  <img src={avatar} alt=""/>
                </div>
                {title}
                <span
                  className="ant-btn-icon"
                  onClick={e => {
                    e.stopPropagation();
                    setVoicePlaying(prevState => (prevState !== value ? value : null));
                  }}
                >
                  {isPlaying ? <PauseCircle24/> : <PlayAround24/>}
                </span>
              </AntButton>
            );
          })}
        </Space>
      </div>
      
      <div>
        <Space direction="horizontal" size="middle" style={{ marginTop: 8 }}>
          {Object.values(SPEED_OPTIONS).map(option => {
            return <AntButton
              key={option.value}
              size="large"
              type={BUTTON.LIGHT_NAVY}
              className="option-speed-btn"
              onClick={() => handleSpeedSelect(option.value)}
              active={selectedSpeed === option.value}
            >
              <span className="option-speed-text">{option.speedText}</span>
              <span>{t(option.lang)}</span>
            </AntButton>;
          })}
        </Space>
      </div>
    
    </div>
  );
};

export default VoiceAndSpeedSelector;