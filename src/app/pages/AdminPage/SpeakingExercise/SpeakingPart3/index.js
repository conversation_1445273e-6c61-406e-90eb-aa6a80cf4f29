import React, { useState } from "react";
import { <PERSON><PERSON>, Card, Input, Table, Popconfirm, Typography, Space, Tooltip } from "antd";
import { DeleteOutlined, PlusOutlined, SoundOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import AI_GEN_STAR from "@src/assets/icons/ai-gen-star.svg";
import "../SpeakingParts.scss";

const { TextArea } = Input;

const SpeakingPart3 = ({
  questions,
  onQuestionsChange,
  onCreateAudio,
  onCreateHint,
  onShowHint,
  audioMap,
  loadingHintPart,
  loadingHintIndex,
  loadingAudioPart,
  loadingAudioIndex,
  onBatchAudioCreation,
  onBatchHintCreation,
  loadingBatchAudioPart,
  loadingBatchHintPart
}) => {
  const { t } = useTranslation();
  const [newQuestion, setNewQuestion] = useState("");

  const handleAddQuestion = () => {
    if (newQuestion.trim()) {
      const updatedQuestions = [
        ...questions,
        {
          id: `part3_${Date.now()}`,
          text: newQuestion.trim(),
          audioId: null
        }
      ];
      onQuestionsChange(updatedQuestions);
      setNewQuestion("");
    }
  };

  const handleDeleteQuestion = (index) => {
    const updatedQuestions = [...questions];
    updatedQuestions.splice(index, 1);
    onQuestionsChange(updatedQuestions);
  };

  const handleQuestionChange = (index, value) => {
    const updatedQuestions = [...questions];
    updatedQuestions[index].text = value;
    onQuestionsChange(updatedQuestions);
  };

  const handleCreateAudio = (index) => {
    onCreateAudio(questions[index].text, "part3", index);
  };

  const handleCreateHint = (index) => {
    onCreateHint(questions[index].text, "part3", index);
  };

  const handleShowHint = (index) => {
    onShowHint(questions[index].hint, "part3", index);
  };

  return (
    <Card
      title={<Typography.Title level={4}>{t("SPEAKING_PART3_TITLE")}</Typography.Title>}
      className="speaking-part3-container"
      extra={<Typography.Text type="secondary">{t("SPEAKING_PART3_SUBTITLE")}</Typography.Text>}
    >
      {/*<Typography.Paragraph className="part-description">*/}
      {/*  {t("SPEAKING_PART3_DESCRIPTION")}*/}
      {/*</Typography.Paragraph>*/}

      <Table
        dataSource={questions.map((question, index) => ({
          ...question,
          key: question.id,
          index: index
        }))}
        pagination={false}
        className="speaking-questions-table"
        columns={[
          {
            title: '#',
            dataIndex: 'index',
            key: 'index',
            width: 60,
            render: (text) => <span className="question-number">{text + 1}.</span>
          },
          {
            title: 'Question',
            dataIndex: 'text',
            key: 'text',
            render: (text, record) => (
              <TextArea
                value={text}
                onChange={(e) => handleQuestionChange(record.index, e.target.value)}
                autoSize={{ minRows: 1, maxRows: 1 }}
                className="question-textarea"
              />
            )
          },
          {
            title: 'Audio',
            dataIndex: 'audioId',
            key: 'audio',
            width: 300,
            render: (audioId, record) => (
              audioId && audioMap[audioId] ? (
                <audio
                  src={audioMap[audioId]}
                  controls
                  className="question-audio"
                />
              ) : null
            )
          },
          {
            title: 'Actions',
            key: 'actions',
            width: 150,
            render: (_, record) => (
              <Space>
                <Tooltip
                  title={record.audioId ? t("RECREATE_AUDIO_TOOLTIP") : t("CREATE_AUDIO_TOOLTIP")}
                  placement="top"
                >
                  <Button
                    icon={<SoundOutlined />}
                    onClick={() => handleCreateAudio(record.index)}
                    type={record.audioId ? "primary" : "default"}
                    className={!record.audioId ? "audio-btn-default" : ""}
                    size="small"
                    loading={loadingAudioPart === "part3" && loadingAudioIndex === record.index}
                  />
                </Tooltip>
                <Tooltip
                  title={record.hint ? t("SHOW_HINT_TOOLTIP") : t("CREATE_HINT_TOOLTIP")}
                  placement="top"
                >
                  <Button
                    onClick={() => record.hint ? handleShowHint(record.index) : handleCreateHint(record.index)}
                    size="small"
                    className={record.hint ? "ai-hint-btn show-hint" : "ai-hint-btn"}
                    loading={!record.hint && loadingHintPart === "part3" && loadingHintIndex === record.index}
                  >
                    {!(loadingHintPart === "part3" && loadingHintIndex === record.index) &&
                      <img src={AI_GEN_STAR} alt="AI Generate Hint" className="ai-gen-star-icon" />}
                    {record.hint && <span className="hint-badge"></span>}
                  </Button>
                </Tooltip>
                <Popconfirm
                  title={t("DELETE_QUESTION_CONFIRM")}
                  description={t("DELETE_QUESTION_DESCRIPTION")}
                  onConfirm={() => handleDeleteQuestion(record.index)}
                  okText={t("YES")}
                  cancelText={t("NO")}
                  placement="left"
                  okButtonProps={{ danger: true }}
                  icon={<DeleteOutlined style={{ color: 'red' }} />}
                >
                  <Tooltip title={t("DELETE_QUESTION_TOOLTIP")} placement="top">
                    <Button danger icon={<DeleteOutlined />} size="small" />
                  </Tooltip>
                </Popconfirm>
              </Space>
            )
          }
        ]}
        footer={() => (
          <div className="add-question-container">
            <div className="add-question-label">{t("ADD_NEW_QUESTION")}</div>
            <TextArea
              value={newQuestion}
              onChange={(e) => setNewQuestion(e.target.value)}
              placeholder={t("NEW_DISCUSSION_QUESTION_PLACEHOLDER")}
              autoSize={{ minRows: 1, maxRows: 3 }}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  handleAddQuestion();
                }
              }}
            />
            <Tooltip title={t("ADD_QUESTION_TOOLTIP")} placement="top">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddQuestion}
                disabled={!newQuestion.trim()}
              >
                {t("ADD_QUESTION")}
              </Button>
            </Tooltip>
          </div>
        )}
      />

      <div className="batch-operations-container">
        {/*<div className="batch-operations-label">{t("BATCH_OPERATIONS")}</div>*/}
        <div className="batch-operations-buttons">
          <Button
            type="primary"
            icon={<SoundOutlined />}
            onClick={() => onBatchAudioCreation("part3")}
            loading={loadingBatchAudioPart === "part3"}
            disabled={questions.filter(q => !q.audioId).length === 0}
            className="batch-operation-button"
          >
            {t("CREATE_AUDIO_FOR_ALL")}
          </Button>
          <Button
            type="primary"
            icon={<img src={AI_GEN_STAR} alt="AI" className="ai-gen-star-icon" />}
            onClick={() => onBatchHintCreation("part3")}
            loading={loadingBatchHintPart === "part3"}
            disabled={questions.filter(q => !q.hint).length === 0}
            className="batch-operation-button ai-batch-button"
          >
            {t("CREATE_HINT_FOR_ALL")}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default SpeakingPart3;
