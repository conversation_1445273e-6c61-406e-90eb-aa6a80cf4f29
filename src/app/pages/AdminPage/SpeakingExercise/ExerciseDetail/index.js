import React, {useEffect, useState} from "react";
import {useNavigate, useParams} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {Button, Form, Input, InputNumber, Modal, Select} from "antd";
import {SaveOutlined, PlusOutlined, ReloadOutlined, StarOutlined} from "@ant-design/icons";
import AI_GEN_STAR from "@src/assets/icons/ai-gen-star.svg";

import {toast} from "@component/ToastProvider";

import Loading from "@component/Loading";
import {AntForm} from "@component/AntForm";

import RULE from "@rule";

import {
  createExercise,
  getExerciseDetail,
  updateExercise,
  createQuestionAudio,
  createHint,
} from "@services/SpeakingExercise";

import "./ExerciseDetail.scss";


import {DIFFICULTY_OPTIONS, EXERCISE_STATUS_OPTIONS, EXERCISE_TYPE_OPTIONS} from "@constant";
import {LINK} from "@link";
import VoiceAndSpeedSelector from "@app/pages/AdminPage/SpeakingExercise/VoiceAndSpeedSelector";
import UploadImagePreview from "@component/UploadImagePreview";
import {uploadFile} from "@services/File";
import {API} from "@api";

import SpeakingPart1 from "../SpeakingPart1";
import SpeakingPart2 from "../SpeakingPart2";
import SpeakingPart3 from "../SpeakingPart3";

function ExerciseDetail() {
  const {t} = useTranslation();
  const exerciseId = useParams().id;

  const [formExercise] = Form.useForm();
  const navigate = useNavigate();
  const [isLoadingInfo, setLoadingInfo] = useState(false);
  const [isLoadingUpload, setLoadingUpload] = useState(false);

  const [exerciseData, setExerciseData] = useState({});
  const [isModified, setIsModified] = useState(false);

  const [isVoiceModalVisible, setIsVoiceModalVisible] = useState(false);
  const [selectedVoiceForCreation, setSelectedVoiceForCreation] = useState("alloy");
  const [selectedSpeedForCreation, setSelectedSpeedForCreation] = useState(1);

  // State for question audio creation
  const [currentQuestionText, setCurrentQuestionText] = useState("");
  const [currentQuestionType, setCurrentQuestionType] = useState("");
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(-1);
  const [loadingAudioPart, setLoadingAudioPart] = useState(null);
  const [loadingAudioIndex, setLoadingAudioIndex] = useState(null);

  // State for batch operations
  const [loadingBatchAudioPart, setLoadingBatchAudioPart] = useState(null);
  const [loadingBatchHintPart, setLoadingBatchHintPart] = useState(null);
  const [isBatchVoiceModalVisible, setIsBatchVoiceModalVisible] = useState(false);
  const [currentBatchPart, setCurrentBatchPart] = useState(null);

  const [avatarId, setAvatarId] = useState(null);
  const [isLoadingAvatar, setLoadingAvatar] = useState(false);
  const [loadingHintIndex, setLoadingHintIndex] = useState(null);
  const [loadingHintPart, setLoadingHintPart] = useState(null);

  // State for IELTS Speaking parts
  const [part1Questions, setPart1Questions] = useState([]);
  const [part2Questions, setPart2Questions] = useState([]);
  const [part3Questions, setPart3Questions] = useState([]);

  // Audio map to store audio URLs
  const [audioMap, setAudioMap] = useState({});


  useEffect(() => {
    if (exerciseId) {
      getSpeakingExerciseData();
    }
  }, [exerciseId]);

  async function getSpeakingExerciseData() {
    const apiResponse = await getExerciseDetail(exerciseId);
    if (apiResponse) {
      setExerciseData(apiResponse);
      setAvatarId(apiResponse.avatarId);

      // Set form values
      formExercise.setFieldsValue({
        title: apiResponse.title,
        status: apiResponse.status,
        avatarId: apiResponse.avatarId,
        topic: apiResponse.topic
      });

      // Xử lý dữ liệu từ cấu trúc mới
      if (apiResponse.parts && Array.isArray(apiResponse.parts)) {
        // Xử lý Part 1
        const part1 = apiResponse.parts.find(p => p.part === "part1");
        if (part1 && part1.questions) {
          setPart1Questions(part1.questions);

          // Load audio URLs for part1 questions
          part1.questions.forEach(question => {
            if (question.audioId) {
              setAudioMap(prev => ({
                ...prev,
                [question.audioId]: API.STREAM_MEDIA.format(question.audioId)
              }));
            }
          });
        }

        // Xử lý Part 2
        const part2 = apiResponse.parts.find(p => p.part === "part2");
        if (part2) {
          // Xử lý câu hỏi part 2 (cue cards)
          if (part2.questions) {
            setPart2Questions(part2.questions);
            part2.questions.forEach(question => {
              if (question.audioId) {
                setAudioMap(prev => ({
                  ...prev,
                  [question.audioId]: API.STREAM_MEDIA.format(question.audioId)
                }));
              }
            });
          } else if (part2.cueCard) {
            // Xử lý dữ liệu cũ (nếu có)
            const cueCardAsQuestion = {
              id: `part2_cuecard_${Date.now()}`,
              text: part2.cueCard.text,
              audioId: part2.cueCard.audioId
            };

            // Chuyển đổi cueCard thành question
            const questions = part2.questions || [];
            setPart2Questions([cueCardAsQuestion, ...questions]);

            // Cập nhật audioMap
            if (part2.cueCard.audioId) {
              setAudioMap(prev => ({
                ...prev,
                [part2.cueCard.audioId]: API.STREAM_MEDIA.format(part2.cueCard.audioId)
              }));
            }
          }
        }

        // Xử lý Part 3
        const part3 = apiResponse.parts.find(p => p.part === "part3");
        if (part3 && part3.questions) {
          setPart3Questions(part3.questions);
          part3.questions.forEach(question => {
            if (question.audioId) {
              setAudioMap(prev => ({
                ...prev,
                [question.audioId]: API.STREAM_MEDIA.format(question.audioId)
              }));
            }
          });
        }
      } else {
        // Xử lý dữ liệu theo cấu trúc cũ (nếu có)
        if (apiResponse.part1Questions) {
          setPart1Questions(apiResponse.part1Questions);
          apiResponse.part1Questions.forEach(question => {
            if (question.audioId) {
              setAudioMap(prev => ({
                ...prev,
                [question.audioId]: API.STREAM_MEDIA.format(question.audioId)
              }));
            }
          });
        }

        // Xử lý dữ liệu cũ cho Part 2 (nếu có)
        if (apiResponse.part2CueCard || apiResponse.part2FollowUpQuestions) {
          const questions = [];

          // Chuyển đổi cueCard thành question
          if (apiResponse.part2CueCard) {
            const cueCardAsQuestion = {
              id: `part2_cuecard_${Date.now()}`,
              text: apiResponse.part2CueCard.text,
              audioId: apiResponse.part2CueCard.audioId
            };
            questions.push(cueCardAsQuestion);

            if (apiResponse.part2CueCard.audioId) {
              setAudioMap(prev => ({
                ...prev,
                [apiResponse.part2CueCard.audioId]: API.STREAM_MEDIA.format(apiResponse.part2CueCard.audioId)
              }));
            }
          }

          // Thêm các follow-up questions vào danh sách questions
          if (apiResponse.part2FollowUpQuestions) {
            apiResponse.part2FollowUpQuestions.forEach(question => {
              questions.push({
                id: question.id || `part2_question_${Date.now()}_${questions.length}`,
                text: question.text,
                audioId: question.audioId
              });

              if (question.audioId) {
                setAudioMap(prev => ({
                  ...prev,
                  [question.audioId]: API.STREAM_MEDIA.format(question.audioId)
                }));
              }
            });
          }

          setPart2Questions(questions);
        }

        if (apiResponse.part3Questions) {
          setPart3Questions(apiResponse.part3Questions);
          apiResponse.part3Questions.forEach(question => {
            if (question.audioId) {
              setAudioMap(prev => ({
                ...prev,
                [question.audioId]: API.STREAM_MEDIA.format(question.audioId)
              }));
            }
          });
        }
      }
    }
  }

  function validateRequestData(formData) {
    const warnings = [
      {condition: !formData.title, message: "TITLE_IS_REQUIRED"},
      {condition: !formData.topic, message: "TOPIC_IS_REQUIRED"}
    ];

    for (const {condition, message} of warnings) {
      if (condition) {
        toast.warning({description: t(message)});
        return false;
      }
    }

    return true;
  }

  async function handleSave() {
    try {
      const formData = formExercise.getFieldsValue();
      validateRequestData(formData);
      setLoadingInfo(true);

      // Chuẩn bị dữ liệu theo cấu trúc yêu cầu
      const parts = [
        {
          part: "part1",
          questions: part1Questions
        },
        {
          part: "part2",
          questions: part2Questions
        },
        {
          part: "part3",
          questions: part3Questions
        }
      ];

      const apiRequest = {
        title: formData.title,
        status: formData.status || exerciseData.status,
        topic: formData.topic,
        avatarId,
        parts,
        _id: exerciseId || undefined,
      };
      const apiResponse = exerciseId
        ? await updateExercise(apiRequest)
        : await createExercise(apiRequest);

      !exerciseId && navigate(LINK.ADMIN.SPEAKING_EXERCISE_ID.format(apiResponse._id));
      if (apiResponse) {
        const successMessage = exerciseId ? "UPDATE_SUCCESS" : "CREATE_SUCCESS";
        toast.success(successMessage, {replace: true});
      }
    } catch (error) {
      toast.error("Something went wrong. Try again later.");
      console.error("Error during save operation:", error);
    } finally {
      setLoadingInfo(false);
    }
  }


  const handleVoiceModalCancel = () => {
    setIsVoiceModalVisible(false);
  };
  const handleVoiceModalOk = async () => {
    // Close the modal
    setIsVoiceModalVisible(false);

    // Set loading state for specific question
    setLoadingAudioPart(currentQuestionType);
    setLoadingAudioIndex(currentQuestionIndex);

    try {
      if (currentQuestionType) {
        // Creating audio for a specific question
        const {audioId} = await createQuestionAudio({
          text: currentQuestionText,
          voice: selectedVoiceForCreation,
          speed: selectedSpeedForCreation,
        });

        // Update the audio URL in the audioMap
        setAudioMap(prev => ({
          ...prev,
          [audioId]: API.STREAM_MEDIA.format(audioId)
        }));

        // Update the question with the new audioId
        if (currentQuestionType === 'part1') {
          const updatedQuestions = [...part1Questions];
          if (updatedQuestions[currentQuestionIndex]) {
            updatedQuestions[currentQuestionIndex] = {
              ...updatedQuestions[currentQuestionIndex],
              audioId: audioId
            };
            setPart1Questions([...updatedQuestions]);
            // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
            setIsModified(true);
          }
        } else if (currentQuestionType === 'part2') {
          const updatedQuestions = [...part2Questions];
          if (updatedQuestions[currentQuestionIndex]) {
            updatedQuestions[currentQuestionIndex] = {
              ...updatedQuestions[currentQuestionIndex],
              audioId: audioId
            };
            setPart2Questions([...updatedQuestions]);
            // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
            setIsModified(true);
          }
        } else if (currentQuestionType === 'part3') {
          const updatedQuestions = [...part3Questions];
          if (updatedQuestions[currentQuestionIndex]) {
            updatedQuestions[currentQuestionIndex] = {
              ...updatedQuestions[currentQuestionIndex],
              audioId: audioId
            };
            setPart3Questions([...updatedQuestions]);
            // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
            setIsModified(true);
          }
        }

        toast.success(t("AUDIO_CREATED_SUCCESS"));
      }
    } catch (error) {
      console.error("Error in handleVoiceModalOk:", error);
      toast.error(t("AUDIO_CREATION_FAILED"));
    } finally {
      // Clear loading state
      setLoadingAudioPart(null);
      setLoadingAudioIndex(null);

      // Reset current question data
      setCurrentQuestionText("");
      setCurrentQuestionType("");
      setCurrentQuestionIndex(-1);
    }
  };


  const handleVoiceChangeFromSelector = (voiceName) => {
    setSelectedVoiceForCreation(voiceName);
  };

  const handleSpeedChangeFromSelector = (speed) => {
    setSelectedSpeedForCreation(speed);
  };

  // Handle batch audio creation for all questions in a part
  const handleBatchAudioCreation = (partType) => {
    setCurrentBatchPart(partType);
    setIsBatchVoiceModalVisible(true);
  };

  const handleBatchVoiceModalCancel = () => {
    setIsBatchVoiceModalVisible(false);
    setCurrentBatchPart(null);
  };

  const handleBatchVoiceModalOk = async () => {
    // Close the modal
    setIsBatchVoiceModalVisible(false);

    // Set loading state for the entire part
    setLoadingBatchAudioPart(currentBatchPart);

    try {
      let questionsToProcess = [];
      let allQuestions = [];

      // Get all questions based on part type (không chỉ lọc những câu chưa có audio)
      if (currentBatchPart === "part1") {
        allQuestions = [...part1Questions];
        questionsToProcess = [...allQuestions]; // Xử lý tất cả câu hỏi
      } else if (currentBatchPart === "part2") {
        allQuestions = [...part2Questions];
        questionsToProcess = [...allQuestions]; // Xử lý tất cả câu hỏi
      } else if (currentBatchPart === "part3") {
        allQuestions = [...part3Questions];
        questionsToProcess = [...allQuestions]; // Xử lý tất cả câu hỏi
      }

      if (questionsToProcess.length === 0) {
        toast.info(t("NO_QUESTIONS_IN_PART"));
        setLoadingBatchAudioPart(null);
        setCurrentBatchPart(null);
        return;
      }

      // Tạo một map để lưu trữ các câu hỏi theo ID để dễ dàng truy cập sau này
      const questionMap = {};
      questionsToProcess.forEach((question, index) => {
        const key = question.id || question._id
        questionMap[key] = index;
      });
      // Create an array of promises for all questions
      const audioPromises = questionsToProcess.map(async (question, questionIndex) => {
        try {
          // Create audio
          const {audioId} = await createQuestionAudio({
            text: question.text,
            voice: selectedVoiceForCreation,
            speed: selectedSpeedForCreation,
          });

          // Return the question ID, index and the new audioId
          return {questionId: question.id || question._id, questionIndex, audioId};
        } catch (error) {
          console.error(`Error creating audio for question ${question.id}:`, error);
          return {questionId: question.id || question._id, questionIndex, error};
        }
      });

      // Wait for all promises to resolve
      const results = await Promise.all(audioPromises);

      // Process results and update audioMap and questions
      const newAudioMap = {...audioMap};
      let successCount = 0;

      // Tạo một bản sao mới của allQuestions để cập nhật
      const updatedQuestions = [...allQuestions];

      results.forEach(result => {
        if (result.audioId) {
          // Update audio map
          newAudioMap[result.audioId] = API.STREAM_MEDIA.format(result.audioId);

          // Sử dụng index được lưu trữ trong kết quả thay vì tìm lại
          const index = questionMap[result.questionId];
          if (index !== undefined && updatedQuestions[index]) {
            updatedQuestions[index] = {
              ...updatedQuestions[index],
              audioId: result.audioId
            };
          }

          successCount++;
        }
      });

      // Update the audioMap state
      setAudioMap(newAudioMap);

      // Update the state with all questions including the updated ones
      if (currentBatchPart === "part1") {
        setPart1Questions(updatedQuestions);
      } else if (currentBatchPart === "part2") {
        setPart2Questions(updatedQuestions);
      } else if (currentBatchPart === "part3") {
        // Đảm bảo cập nhật đúng cách cho part3
        setPart3Questions(updatedQuestions);
      }

      if (successCount > 0) {
        toast.success(t("BATCH_AUDIO_CREATED_SUCCESS"));

        // Mark as modified to ensure data is saved
        setIsModified(true);
      } else {
        toast.error(t("BATCH_AUDIO_CREATION_FAILED"));
      }
    } catch (error) {
      console.error("Error in batch audio creation:", error);
      toast.error(t("BATCH_AUDIO_CREATION_FAILED"));
    } finally {
      // Clear loading state
      setLoadingBatchAudioPart(null);
      setCurrentBatchPart(null);
    }
  };

  // Handle batch hint creation for all questions in a part
  const handleBatchHintCreation = async (partType) => {
    // Set loading state for the entire part
    setLoadingBatchHintPart(partType);

    try {
      let questionsToProcess = [];
      let allQuestions = [];

      // Get all questions based on part type (không chỉ lọc những câu chưa có hint)
      if (partType === "part1") {
        allQuestions = [...part1Questions];
        questionsToProcess = [...allQuestions]; // Xử lý tất cả câu hỏi
      } else if (partType === "part2") {
        allQuestions = [...part2Questions];
        questionsToProcess = [...allQuestions]; // Xử lý tất cả câu hỏi
      } else if (partType === "part3") {
        allQuestions = [...part3Questions];
        questionsToProcess = [...allQuestions]; // Xử lý tất cả câu hỏi
      }

      if (questionsToProcess.length === 0) {
        toast.info(t("NO_QUESTIONS_IN_PART"));
        setLoadingBatchHintPart(null);
        return;
      }

      // Tạo một map để lưu trữ các câu hỏi theo ID để dễ dàng truy cập sau này
      const questionMap = {};
      questionsToProcess.forEach((question, index) => {
        const key = question.id || question._id
        questionMap[key] = index;
      });

      // Create an array of promises for all questions
      const hintPromises = questionsToProcess.map(async (question, questionIndex) => {
        try {
          const apiRequest = {
            text: question.text,
            part: partType,
            exerciseId: exerciseId
          };

          const response = await createHint(apiRequest);
          if (response && response.text) {
            return {questionId: question.id || question._id, questionIndex, hintText: response.text};
          }
          return {questionId: question.id || question._id, questionIndex, error: "No hint text returned"};
        } catch (error) {
          console.error(`Error creating hint for question ${question.id}:`, error);
          return {questionId: question.id || question._id, questionIndex, error};
        }
      });

      // Wait for all promises to resolve
      const results = await Promise.all(hintPromises);

      // Process results and update questions
      let successCount = 0;

      // Tạo một bản sao mới của allQuestions để cập nhật
      const updatedQuestions = [...allQuestions];

      results.forEach(result => {
        if (result.hintText) {
          // Sử dụng index được lưu trữ trong kết quả thay vì tìm lại
          const index = questionMap[result.questionId];
          if (index !== undefined && updatedQuestions[index]) {
            updatedQuestions[index] = {
              ...updatedQuestions[index],
              hint: result.hintText
            };
          }

          successCount++;
        }
      });

      // Update the state with all questions including the updated ones
      if (partType === "part1") {
        setPart1Questions(updatedQuestions);
      } else if (partType === "part2") {
        setPart2Questions(updatedQuestions);
      } else if (partType === "part3") {
        // Đảm bảo cập nhật đúng cách cho part3
        setPart3Questions(updatedQuestions);
      }
      if (successCount > 0) {
        toast.success(t("BATCH_HINT_CREATED_SUCCESS"));

        // Mark as modified to ensure data is saved
        setIsModified(true);
      } else {
        toast.error(t("BATCH_HINT_CREATION_FAILED"));
      }
    } catch (error) {
      console.error("Error in batch hint creation:", error);
      toast.error(t("BATCH_HINT_CREATION_FAILED"));
    } finally {
      // Clear loading state
      setLoadingBatchHintPart(null);
    }
  };

  async function handleUploadImage(file) {
    setLoadingAvatar(true);
    const apiResponse = await uploadFile(file, {folder: "image"});
    if (apiResponse) {
      setAvatarId(apiResponse._id);
      formExercise.setFieldsValue({avatarId: apiResponse._id});
    }
    setLoadingAvatar(false);
  }

  async function handleClearImage() {
    setAvatarId(null);
    formExercise.resetFields(["avatarId"]);
  }

  // Prepare UI
  // Handle creating audio for a specific question
  const handleCreateQuestionAudio = (questionText, questionType, questionIndex) => {
    setCurrentQuestionText(questionText);
    setCurrentQuestionType(questionType);
    setCurrentQuestionIndex(questionIndex);
    setIsVoiceModalVisible(true);
    setEditedHintText(questionText); // Initialize edited hint text with question text
  };

  // Handle creating hint for a specific question
  const handleCreateHint = async (questionText, questionType, questionIndex) => {
    try {
      // Set loading state for specific question
      setLoadingHintPart(questionType);
      setLoadingHintIndex(questionIndex);

      const apiRequest = {
        text: questionText,
        part: questionType,
        exerciseId: exerciseId
      };

      const response = await createHint(apiRequest);
      if (response && response.text) {
        // Update the question with the hint
        let updatedQuestions;
        const hintText = response.text;

        if (questionType === "part1") {
          updatedQuestions = [...part1Questions];
          if (updatedQuestions[questionIndex]) {
            updatedQuestions[questionIndex] = {
              ...updatedQuestions[questionIndex],
              hint: hintText
            };
            setPart1Questions([...updatedQuestions]);
            // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
            setIsModified(true);
          }
        } else if (questionType === "part2") {
          updatedQuestions = [...part2Questions];
          if (updatedQuestions[questionIndex]) {
            updatedQuestions[questionIndex] = {
              ...updatedQuestions[questionIndex],
              hint: hintText
            };
            setPart2Questions([...updatedQuestions]);
            // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
            setIsModified(true);
          }
        } else if (questionType === "part3") {
          updatedQuestions = [...part3Questions];
          if (updatedQuestions[questionIndex]) {
            updatedQuestions[questionIndex] = {
              ...updatedQuestions[questionIndex],
              hint: hintText
            };
            setPart3Questions([...updatedQuestions]);
            // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
            setIsModified(true);
          }
        }

        // Update the current hint text if we're in the modal
        if (isHintModalVisible &&
          currentHintQuestionType === questionType &&
          currentHintQuestionIndex === questionIndex) {
          setCurrentHintText(hintText);
          setEditedHintText(hintText);
        } else {
          // If modal is not visible, show it with the new hint
          setCurrentHintText(hintText);
          setEditedHintText(hintText);
          setCurrentHintQuestionType(questionType);
          setCurrentHintQuestionIndex(questionIndex);
          setCurrentHintQuestionText(questionText);
          setIsHintModalVisible(true);
        }

        toast.success(t("CREATE_HINT_SUCCESS"));
      } else {
        toast.error(t("CREATE_HINT_ERROR"));
      }
    } catch (error) {
      console.error("Error creating hint:", error);
      toast.error(t("CREATE_HINT_ERROR"));
    } finally {
      // Clear loading state
      setLoadingHintPart(null);
      setLoadingHintIndex(null);
    }
  };

  // Handle showing hint in a modal
  const [isHintModalVisible, setIsHintModalVisible] = useState(false);
  const [currentHintText, setCurrentHintText] = useState("");
  const [currentHintQuestionType, setCurrentHintQuestionType] = useState("");
  const [currentHintQuestionIndex, setCurrentHintQuestionIndex] = useState(-1);
  const [currentHintQuestionText, setCurrentHintQuestionText] = useState("");
  const [editedHintText, setEditedHintText] = useState("");
  const [isSavingHint, setIsSavingHint] = useState(false);

  const handleShowHint = (hintText, questionType, questionIndex) => {
    setCurrentHintText(hintText);
    setCurrentHintQuestionType(questionType);
    setCurrentHintQuestionIndex(questionIndex);
    setEditedHintText(hintText); // Initialize edited hint text with current hint

    // Get the question text
    let questionText = "";
    if (questionType === "part1" && part1Questions[questionIndex]) {
      questionText = part1Questions[questionIndex].text;
    } else if (questionType === "part2" && part2Questions[questionIndex]) {
      questionText = part2Questions[questionIndex].text;
    } else if (questionType === "part3" && part3Questions[questionIndex]) {
      questionText = part3Questions[questionIndex].text;
    }

    setCurrentHintQuestionText(questionText);
    setIsHintModalVisible(true);
  };

  const handleHintModalCancel = () => {
    setIsHintModalVisible(false);
  };

  const handleRecreateHint = async () => {
    try {
      // Set loading state for specific question
      setLoadingHintPart(currentHintQuestionType);
      setLoadingHintIndex(currentHintQuestionIndex);

      // Get the question text based on the current hint type and index
      let questionText = currentHintQuestionText;

      const apiRequest = {
        text: questionText,
        part: currentHintQuestionType,
        exerciseId: exerciseId
      };

      const response = await createHint(apiRequest);
      if (response && response.text) {
        // Update the question with the hint
        let updatedQuestions;
        const hintText = response.text;

        if (currentHintQuestionType === "part1") {
          updatedQuestions = [...part1Questions];
          if (updatedQuestions[currentHintQuestionIndex]) {
            updatedQuestions[currentHintQuestionIndex] = {
              ...updatedQuestions[currentHintQuestionIndex],
              hint: hintText
            };
            setPart1Questions([...updatedQuestions]);
            // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
            setIsModified(true);
          }
        } else if (currentHintQuestionType === "part2") {
          updatedQuestions = [...part2Questions];
          if (updatedQuestions[currentHintQuestionIndex]) {
            updatedQuestions[currentHintQuestionIndex] = {
              ...updatedQuestions[currentHintQuestionIndex],
              hint: hintText
            };
            setPart2Questions([...updatedQuestions]);
            // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
            setIsModified(true);
          }
        } else if (currentHintQuestionType === "part3") {
          updatedQuestions = [...part3Questions];
          if (updatedQuestions[currentHintQuestionIndex]) {
            updatedQuestions[currentHintQuestionIndex] = {
              ...updatedQuestions[currentHintQuestionIndex],
              hint: hintText
            };
            setPart3Questions([...updatedQuestions]);
            // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
            setIsModified(true);
          }
        }

        // Update the current hint text and edited hint text in the popup
        setCurrentHintText(hintText);
        setEditedHintText(hintText);

        toast.success(t("CREATE_HINT_SUCCESS"));
      } else {
        toast.error(t("CREATE_HINT_ERROR"));
      }
    } catch (error) {
      console.error("Error recreating hint:", error);
      toast.error(t("CREATE_HINT_ERROR"));
    } finally {
      // Clear loading state
      setLoadingHintPart(null);
      setLoadingHintIndex(null);
    }
  };

  const handleSaveEditedHint = async () => {
    try {
      setIsSavingHint(true);

      // Update the hint in the appropriate questions array
      let updatedQuestions;

      if (currentHintQuestionType === "part1") {
        updatedQuestions = [...part1Questions];
        if (updatedQuestions[currentHintQuestionIndex]) {
          updatedQuestions[currentHintQuestionIndex] = {
            ...updatedQuestions[currentHintQuestionIndex],
            hint: editedHintText
          };
          setPart1Questions([...updatedQuestions]);
          // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
          setIsModified(true);
        }
      } else if (currentHintQuestionType === "part2") {
        updatedQuestions = [...part2Questions];
        if (updatedQuestions[currentHintQuestionIndex]) {
          updatedQuestions[currentHintQuestionIndex] = {
            ...updatedQuestions[currentHintQuestionIndex],
            hint: editedHintText
          };
          setPart2Questions([...updatedQuestions]);
          // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
          setIsModified(true);
        }
      } else if (currentHintQuestionType === "part3") {
        updatedQuestions = [...part3Questions];
        if (updatedQuestions[currentHintQuestionIndex]) {
          updatedQuestions[currentHintQuestionIndex] = {
            ...updatedQuestions[currentHintQuestionIndex],
            hint: editedHintText
          };
          setPart3Questions([...updatedQuestions]);
          // Đánh dấu đã sửa đổi để đảm bảo dữ liệu được lưu
          setIsModified(true);
        }
      }

      // Update the current hint text
      setCurrentHintText(editedHintText);

      toast.success(t("UPDATE_HINT_SUCCESS"));
    } catch (error) {
      console.error("Error saving edited hint:", error);
      toast.error(t("UPDATE_HINT_ERROR"));
    } finally {
      setIsSavingHint(false);
    }
  };

  return <>
    <div className="speaking-exercise-detail-container">
      <div className="speaking-exercise-detail__header">
        <div className="header-content">
          <div className="header-title">{exerciseId ? t("EDIT_SPEAKING_EXERCISE") : t("CREATE_SPEAKING_EXERCISE")}</div>
          <div className="header-description">{t("SPEAKING_EXERCISE_DETAIL_DESCRIPTION")}</div>
        </div>
      </div>

      <Loading active={isLoadingInfo || isLoadingUpload} className="speaking-exercise-detail__section">
        <div className="exercise-info-container">
          <div className="info-header">
            <h3 className="section-title">{t("BASIC_INFO")}</h3>
          </div>
          <div className="info-content">
            <AntForm
              form={formExercise}
              layout="vertical"
              requiredMark={true}
              className="form-exercise-info"
            >
              <div className="form-row">
                <AntForm.Item
                  label={t("TITLE")}
                  name="title"
                  rules={[RULE.REQUIRED]}
                  tooltip={t("EXERCISE_TITLE_TOOLTIP")}
                  className="title-field"
                >
                  <Input
                    placeholder={t("ENTER_TITLE")}
                    size="large"
                  />
                </AntForm.Item>

                <AntForm.Item
                  label={t("TOPIC")}
                  name="topic"
                  rules={[RULE.REQUIRED]}
                  tooltip={t("EXERCISE_TOPIC_TOOLTIP")}
                  className="topic-field"
                >
                  <Input
                    placeholder={t("ENTER_TOPIC")}
                    size="large"
                  />
                </AntForm.Item>

                <AntForm.Item
                  label={t("STATUS")}
                  name="status"
                  rules={[RULE.REQUIRED]}
                  tooltip={t("EXERCISE_STATUS_TOOLTIP")}
                  className="status-field"
                >
                  <Select
                    placeholder={t("SELECT_STATUS")}
                    options={EXERCISE_STATUS_OPTIONS}
                    size="large"
                  />
                </AntForm.Item>
              </div>

              <div className="thumbnail-row">
                <h3 className="section-title">{t("EXERCISE_THUMBNAIL")}</h3>
                <div className="avatar-upload-container">
                  <UploadImagePreview
                    loading={isLoadingAvatar}
                    onDrop={handleUploadImage}
                    onClear={handleClearImage}
                    imageId={avatarId}
                    className="avatar-uploader"
                  />
                </div>
              </div>
            </AntForm>
          </div>
        </div>

        <AntForm
          form={formExercise}
          layout="vertical"
          requiredMark={true}
          className="form-hidden-fields"
          hidden
        >

          <AntForm.Item hidden name="avatarId">
            <Input readOnly/>
          </AntForm.Item>

          <AntForm.Item hidden name="tag">
            <Input readOnly/>
          </AntForm.Item>

          <AntForm.Item hidden name="timeLimit">
            <InputNumber readOnly/>
          </AntForm.Item>

          <AntForm.Item hidden name="difficulty">
            <Input readOnly value="B1"/>
          </AntForm.Item>

          <AntForm.Item hidden name="type">
            <Input readOnly value="speaking"/>
          </AntForm.Item>
        </AntForm>


      </Loading>

      <SpeakingPart1
        questions={part1Questions}
        onQuestionsChange={setPart1Questions}
        onCreateAudio={handleCreateQuestionAudio}
        onCreateHint={handleCreateHint}
        onShowHint={handleShowHint}
        audioMap={audioMap}
        loadingHintPart={loadingHintPart}
        loadingHintIndex={loadingHintIndex}
        loadingAudioPart={loadingAudioPart}
        loadingAudioIndex={loadingAudioIndex}
        onBatchAudioCreation={handleBatchAudioCreation}
        onBatchHintCreation={handleBatchHintCreation}
        loadingBatchAudioPart={loadingBatchAudioPart}
        loadingBatchHintPart={loadingBatchHintPart}
      />

      <SpeakingPart2
        questions={part2Questions}
        onQuestionsChange={setPart2Questions}
        onCreateAudio={handleCreateQuestionAudio}
        onCreateHint={handleCreateHint}
        onShowHint={handleShowHint}
        audioMap={audioMap}
        loadingHintPart={loadingHintPart}
        loadingHintIndex={loadingHintIndex}
        loadingAudioPart={loadingAudioPart}
        loadingAudioIndex={loadingAudioIndex}
        onBatchAudioCreation={handleBatchAudioCreation}
        onBatchHintCreation={handleBatchHintCreation}
        loadingBatchAudioPart={loadingBatchAudioPart}
        loadingBatchHintPart={loadingBatchHintPart}
      />

      <SpeakingPart3
        questions={part3Questions}
        onQuestionsChange={setPart3Questions}
        onCreateAudio={handleCreateQuestionAudio}
        onCreateHint={handleCreateHint}
        onShowHint={handleShowHint}
        audioMap={audioMap}
        loadingHintPart={loadingHintPart}
        loadingHintIndex={loadingHintIndex}
        loadingAudioPart={loadingAudioPart}
        loadingAudioIndex={loadingAudioIndex}
        onBatchAudioCreation={handleBatchAudioCreation}
        onBatchHintCreation={handleBatchHintCreation}
        loadingBatchAudioPart={loadingBatchAudioPart}
        loadingBatchHintPart={loadingBatchHintPart}
      />

      <div className="save-button-container">
        <Button
          type="primary"
          onClick={handleSave}
          size="large"
          icon={<SaveOutlined/>}
          className="save-button"
        >
          {t("SAVE_DATA")}
        </Button>
      </div>

      <Modal
        width={1200}
        title={currentQuestionType ? "Create Audio for Question" : "Create Audio for Transcript"}
        open={isVoiceModalVisible}
        onOk={handleVoiceModalOk}
        onCancel={handleVoiceModalCancel}
      >
        {currentQuestionType && (
          <div className="question-preview">
            <h3>Question Text:</h3>
            <p>{currentQuestionText}</p>
          </div>
        )}
        <VoiceAndSpeedSelector
          onVoiceChange={handleVoiceChangeFromSelector}
          onSpeedChange={handleSpeedChangeFromSelector}
        />
      </Modal>

      <Modal
        width={1200}
        title={t("CREATE_AUDIO_FOR_ALL_QUESTIONS")}
        open={isBatchVoiceModalVisible}
        onOk={handleBatchVoiceModalOk}
        onCancel={handleBatchVoiceModalCancel}
      >
        <div className="batch-operation-info">
          <p>{t("BATCH_AUDIO_CREATION_INFO")}</p>
        </div>
        <VoiceAndSpeedSelector
          onVoiceChange={handleVoiceChangeFromSelector}
          onSpeedChange={handleSpeedChangeFromSelector}
        />
      </Modal>

      <Modal
        width={1100}
        title={t("QUESTION_HINT")}
        open={isHintModalVisible}
        onCancel={handleHintModalCancel}
        footer={[
          <div key="footer-buttons" className="hint-modal-footer">
            <Button
              key="close"
              onClick={handleHintModalCancel}
              size="large"
              className="modal-button close-button"
            >
              {t("CLOSE")}
            </Button>
            <Button
              key="save"
              type="primary"
              onClick={handleSaveEditedHint}
              loading={isSavingHint}
              size="large"
            >
              {t("SAVE")}
            </Button>
          </div>
        ]}
        className="hint-modal"
      >
        <div className="hint-preview">
          <div className="question-section">
            <h3>{t("QUESTION_TEXT")}:</h3>
            <div className="question-content">
              {currentHintQuestionText}
            </div>
          </div>

          <div className="hint-section">
            <h3>
              <img src={AI_GEN_STAR} alt="AI" className="ai-gen-star-icon"/>
              {t("AI_HINT")}:
              <Button
                type="primary"
                size="small"
                onClick={handleRecreateHint}
                loading={loadingHintPart === currentHintQuestionType && loadingHintIndex === currentHintQuestionIndex}
                className="recreate-hint-button"
              >
                {t("RECREATE_HINT")}
              </Button>
            </h3>
            <div className="hint-edit-container">
              <Input.TextArea
                value={editedHintText}
                onChange={(e) => setEditedHintText(e.target.value)}
                autoSize={{minRows: 5, maxRows: 15}}
                className="hint-edit-textarea"
              />
            </div>
          </div>
        </div>
      </Modal>

    </div>

  </>;
}

export default ExerciseDetail;
