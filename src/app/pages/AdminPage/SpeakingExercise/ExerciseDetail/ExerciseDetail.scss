.speaking-exercise-detail-container {
  background-color: var(--white);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;

  .speaking-exercise-detail__header {
    padding: 16px 20px;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    display: flex;
    align-items: center;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .header-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .header-title {
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
        color: var(--typo-colours-primary-black);
      }

      .header-description {
        font-size: 14px;
        line-height: 20px;
        color: var(--typo-colours-secondary-grey);
      }
    }
  }

  .speaking-exercise-detail__section {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .ant-form-item {
      margin: 0;
    }

    .button-wrapper {
      display: flex;
      justify-content: flex-end;
      gap: 24px;
      margin-top: 16px;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;

      .save-button {
        padding: 0 32px;
        height: 48px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
      }
    }

    .speaking-exercise-detail__section {
      margin-bottom: 40px;
    }
  }

  /* Speaking Part Styles */
  .speaking-part1-container,
  .speaking-part2-container,
  .speaking-part3-container {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, var(--primary-color), #5cbbff);
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 20px 24px;
      background-color: #fafafa;

      .ant-card-head-title {
        padding: 0;
      }

      h4 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #1f1f1f;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          display: inline-block;
          width: 8px;
          height: 8px;
          background-color: var(--primary-color);
          border-radius: 50%;
          margin-right: 12px;
        }
      }
    }

    .ant-card-body {
      padding: 28px;
    }
  }

  .question-preview {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 8px;

    h3 {
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .exercise-info-container {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    }

    .info-header {
      background-color: #f8fafc;
      padding: 20px 32px;
      border-bottom: 1px solid #edf2f7;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f1f1f;
        margin: 0;
        display: flex;
        align-items: center;

        &:before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 18px;
          background-color: var(--primary-color);
          margin-right: 12px;
          border-radius: 2px;
        }
      }
    }

    .info-content {
      padding: 32px;

      .form-exercise-info {
        display: flex;
        flex-direction: column;
        gap: 30px;

        .form-row {
          display: flex;
          flex-direction: row;
          gap: 24px;
          align-items: flex-start;

          .title-field {
            flex: 2;
          }

          .topic-field {
            flex: 2;
          }

          .status-field {
            flex: 2;
          }
        }

        .thumbnail-row {

          .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f1f1f;
            margin-bottom: 16px;
          }

          .avatar-upload-container {
            width: 100%;
            max-width: 120px;
            display: flex;
            align-items: center;
            background-color: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            border: 1px solid #edf2f7;

            .ant-upload-drag {
              border-radius: 10px;
              border: 2px dashed #d9d9d9;
              background-color: #ffffff;
              transition: all 0.3s;
              padding: 20px;
              min-height: 220px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              width: 100%;

              &:hover {
                border-color: var(--primary-color);
                background-color: rgba(24, 144, 255, 0.02);
              }

              .ant-upload-drag-icon {
                color: var(--primary-color);
                font-size: 48px;
                margin-bottom: 16px;
              }

              .ant-upload-text {
                font-size: 16px;
                font-weight: 500;
                color: #333;
                margin-bottom: 8px;
              }

              .ant-upload-hint {
                color: #888;
              }
            }

            .ant-upload-list-picture-card-container {
              width: 100% !important;
              height: 220px !important;
              border-radius: 10px;
              overflow: hidden;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              transition: all 0.3s;
              margin: 0;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }

              .ant-upload-list-item {
                width: 100% !important;
                height: 100% !important;
                border-radius: 10px;
                padding: 0;
                border: none;
                margin: 0;

                img {
                  object-fit: cover;
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }
        }

        .ant-form-item {
          margin: 0;
          width: 100%;

          .ant-form-item-label > label {
            font-weight: 600;
            color: #333;
            font-size: 15px;
          }

          .ant-input, .ant-select {
            border-radius: 8px;
            height: 46px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s;

            &:hover, &:focus {
              border-color: var(--primary-color);
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
            }
          }

          .ant-select-selector {
            border-radius: 8px;
            height: 46px !important;
            display: flex;
            align-items: center;
          }

          .ant-select-selection-item {
            line-height: 46px;
          }
        }
      }
    }
  }

  .form-hidden-fields {
    display: none;
  }

  .speaking-exercise-detail__no-data .no-data-container .no-data {
    width: 80px;
  }

}

.button-wrapper {
  margin-top: 16px;
  display: flex;
  gap: 16px;
  justify-content: flex-end;

  .ant-btn {
    width: 250px;
  }
}

.save-button-container {
  display: flex;
  justify-content: center;
  margin: 0 0 60px;
  width: 100%;

  .save-button {
    min-width: 180px;
    height: 48px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}
