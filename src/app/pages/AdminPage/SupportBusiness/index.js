import React, {useEffect, useState} from "react";
import {useLocation} from "react-router-dom";
import {connect} from "react-redux";
import {Card, Checkbox, Input, Select, Form, Row, Col, Tooltip} from "antd";
import {useTranslation} from "react-i18next";
import {SearchOutlined, InfoCircleOutlined} from "@ant-design/icons";

import Loading from "@src/app/component/Loading";
import TableAdmin from "@src/app/component/TableAdmin";
import AntButton from "@src/app/component/AntButton";
import {AntForm} from "@src/app/component/AntForm";

import {toast} from "@src/app/component/ToastProvider";
import {
  formatTimeDate,
  paginationConfig,
  orderColumn,
  convertQueryToObject,
  handleReplaceUrlSearch,
  handleSearchParams
} from "@src/common/functionCommons";
import {getPaginationSupportBusiness, updateSupportBusiness} from "@src/app/services/SupportBusiness";

import {BUTTON, getTrueFalseOptions, PAGINATION_INIT} from "@constant";
import {handlePagingData} from "@src/common/dataConverter";

import "./SupportBusiness.scss";


function SupportBusiness() {
  const location = useLocation();
  const {t, i18n} = useTranslation();
  const [businessData, setBusinessData] = useState(PAGINATION_INIT);
  const [formFilter] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const {query, paging} = handleSearchParams(location.search);
    getBusinessData(paging, query);

    // Set initial form values from query params
    const queryParams = convertQueryToObject(location.search);
    formFilter.setFieldsValue({
      fullName: queryParams.fullName || "",
      email: queryParams.email || "",
      company: queryParams.company || "",
      hasSupport: queryParams.hasSupport !== undefined ? queryParams.hasSupport : undefined,
    });
  }, [location.search]);

  const getBusinessData = async (paging, query) => {
    setIsLoading(true);
    const apiResponse = await getPaginationSupportBusiness(paging, query, ["company", "fullName"]);
    if (apiResponse) {
      setBusinessData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  };

  const onChangeCheckbox = async (record) => {
    setIsLoading(true);
    const updateResponse = await updateSupportBusiness({_id: record._id, hasSupport: !record.hasSupport});
    if (updateResponse) {
      await getBusinessData(businessData.paging, businessData.query);
      toast.success(t("UPDATE_SUPPORT_BUSINESS_SUCCESS"));
    } else {
      toast.error(t("UPDATE_SUPPORT_BUSINESS_ERROR"));
    }
    setIsLoading(false);
  };


  const columns = [
    orderColumn(businessData.paging),
    {
      title: t("COMPANY_NAME"),
      dataIndex: "company",
      width: 250,
      render: (text) => <span className="company-name-value">{text}</span>,
    },
    {
      title: t("CONTACT_NAME"),
      dataIndex: "fullName",
      width: 180,
    },
    {
      title: t("EMAIL"),
      dataIndex: "email",
      width: 250,
    },
    {
      title: t("PHONE_NUMBER"),
      dataIndex: "phone",
      width: 150,
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      align: "center",
      render: formatTimeDate,
      width: 150,
    },
    {
      title: t("HAS_SUPPORT"),
      dataIndex: "hasSupport",
      align: "center",
      width: 120,
      render: (_, record) => (
        <div className="support-business-actions">
          <Tooltip title={record.hasSupport ? t("REMOVE_SUPPORT") : t("ADD_SUPPORT")}>
            <Checkbox
              onChange={() => onChangeCheckbox(record)}
              checked={record.hasSupport}
              className={record.hasSupport ? "support-active" : ""}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(businessData.paging, businessData.query, i18n.language);

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, businessData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, businessData.paging.pageSize, {});
  };

  return (
    <Loading active={isLoading} transparent>
      <div className="support-business-container">
        <Card className="support-business-info-card">
          <div className="support-business-info-header">
            <div>
              <h1 className="support-business-title">{t("SUPPORT_BUSINESS")}</h1>
              <p className="support-business-description">{t("SUPPORT_BUSINESS_DESCRIPTION")}</p>
            </div>
          </div>
        </Card>

        <Card className="support-business-search-card">
          <AntForm onFinish={onSubmitFilter} layout="horizontal" form={formFilter} className="form-filter" size={"large"}>
            <Row gutter={24}>
              <Col xs={24} md={12} lg={6}>
                <AntForm.Item name="company" className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_COMPANY_NAME_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={12} lg={6}>
                <AntForm.Item name="fullName" className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_CONTACT_NAME_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={12} lg={6}>
                <AntForm.Item name="email" className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_EMAIL_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>

              <Col xs={24} md={12} lg={6}>
                <AntForm.Item name="hasSupport" className="search-form-item">
                  <Select
                    placeholder={t("FILTER_BY_SUPPORT_STATUS")}
                    allowClear
                    options={getTrueFalseOptions()}
                  />
                </AntForm.Item>
              </Col>
            </Row>
            <Row justify="end" className="search-buttons-row">
              <Col>
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="support-business-table-card">
          <TableAdmin
            columns={columns}
            dataSource={businessData.rows}
            scroll={{x: 1000}}
            pagination={pagination}
            rowKey="_id"
            className="support-business-table"
            rowClassName={() => "support-business-table-row"}
            locale={{ emptyText: t("NO_SUPPORT_BUSINESS_FOUND") }}
          />
        </Card>
      </div>
    </Loading>
  );
}

function mapStateToProps(store) {
  const {user} = store.auth;
  return {user};
}

export default connect(mapStateToProps)(SupportBusiness);
