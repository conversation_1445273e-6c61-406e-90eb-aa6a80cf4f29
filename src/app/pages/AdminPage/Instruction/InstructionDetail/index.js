import { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { Tabs } from "antd";
import clsx from "clsx";

import Options from "./Options";
import FineTunings from "./FineTunings";
import DataSet from "./Dataset";
import AntButton from "@src/app/component/AntButton";
import FormInstructionDetail from "@app/pages/AdminPage/Instruction/InstructionDetail/FormInstructionDetail";

import { getDataset, getOptions } from "@services/Option";
import { getAllOutputTypes, getOutputTypes } from "@services/OutputType";
import { getFineTunings } from "@services/FineTuning";
import { getGptModel } from "@src/app/services/GPTModelPrice";
import { getPromptInstruction, getInstructionDetail } from "@services/Instruction";

import { BUTTON } from "@constant";

import "./InstructionDetail.scss";
import PreviewPromptInstruction from "./PreviewPromptInstruction";
import { getAllKnowledge } from "@services/Knowledge";
import { useTranslation } from "react-i18next";

const InstructionDetail = ({ ...props }) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const [activeKeyTab, setActiveKeyTab] = useState("1");
  const [fineTuningList, setFineTuningList] = useState([]);
  const [optionsData, setOptionsData] = useState([]);
  const [outputTypeData, setOutputTypeData] = useState([]);
  const [datasetList, setDatasetList] = useState([]);
  const [gptModelData, setGPTModelData] = useState([]);
  const [promptData, setPromptData] = useState([]);
  const [knowledgeData, setKnowledgeData] = useState([]);
  const [instructionData, setInstructionData] = useState();
  const outputTypeCode = instructionData?.outputTypeId?.code;

  const [modalDataset, setModalDataset] = useState({
    open: false,
    recordId: null,
  });
  const [isShowModalFineTuning, setShowModalFineTuning] = useState(false);
  const [isShowModalPreviewPrompt, setShowModalPreviewPrompt] = useState(false);

  const gptModelOptions = gptModelData.map((item) => ({ label: item.gptModel, value: item.gptModel }));

  useEffect(() => {
    if (id) {
      Promise.all([
        getFineTuningsData(),
        getDatasetByInstruction(),
        getKnowledge(),
        getInstructionData(),
      ]);
    }
  }, [id]);

  useEffect(() => {
    Promise.all([
      getOutputTypeData(),
      getGptModelData(),
      getOptionsData(),
    ]);
  }, []);

  async function getGptModelData() {
    const apiResponse = await getGptModel();
    if (apiResponse) {
      setGPTModelData(apiResponse);
    }
  }

  async function getKnowledge() {
    const dataResponse = await getAllKnowledge();
    if (dataResponse) {
      setKnowledgeData(dataResponse);
    }
  }

  async function getInstructionData() {
    const dataResponse = await getInstructionDetail(id);
    if (dataResponse) {
      setInstructionData(dataResponse);
    }
  }

  async function getOutputTypeData() {
    const dataResponse = await getOutputTypes();

    if (dataResponse) {
      setOutputTypeData(dataResponse);
    }
  }


  const modelOptions = useMemo(() => {
    const optionsFineTuning = fineTuningList.reduce((options, fineTuning) => {
      const newOption = {
        label: fineTuning.fineTunedModel,
        value: fineTuning.fineTunedModel,
      };
      return fineTuning.fineTunedModel ? [...options, newOption] : options;
    }, []);
    return [...gptModelOptions, ...optionsFineTuning];
  }, [fineTuningList, gptModelOptions]);


  const getFineTuningsData = async () => {
    const query = { instructionId: id };
    const dataResponse = await getFineTunings(query);
    if (dataResponse) {
      setFineTuningList(dataResponse);
    }
  };

  const getOptionsData = async () => {
    const dataResponse = await getOptions();
    if (dataResponse) {
      setOptionsData(dataResponse);
    }
  };
  const getDatasetByInstruction = async () => {
    const query = { instructionId: id };
    const dataResponse = await getDataset(query);
    if (dataResponse) {
      setDatasetList(dataResponse);
    }
  };

  const getPromptData = async () => {
    const dataResponse = await getPromptInstruction(id);
    if (dataResponse) {
      setPromptData(dataResponse);
    }
  };
  const onChangeTab = (key) => {
    setActiveKeyTab(key);
  };

  const tabItems = [
    {
      label: "Instruction detail",
      key: "1",
      children: (
        <FormInstructionDetail
          modelOptions={modelOptions}
          knowledgeData={knowledgeData}
          outputTypeData={outputTypeData}
          optionsData={optionsData}
          instructionData={instructionData}
        />
      ),
    },
    {
      label: "Refined Dataset",
      key: "2",
      children: <DataSet
        InstructionID={id} modalDataset={modalDataset} setDatasetList={setDatasetList}
        setModalDataset={setModalDataset} samplesData={datasetList}
        handleReloadData={getDatasetByInstruction}
        outputTypeCode={outputTypeCode} />,
    },
    {
      label: "Fine tuned model",
      key: "3",
      children: (
        <FineTunings
          isShowModalFineTuning={isShowModalFineTuning}
          setShowModalFineTuning={setShowModalFineTuning}
          fineTuningList={fineTuningList}
          handleReloadData={getFineTuningsData}
          dataSets={datasetList}
          modelOptions={modelOptions}
        />
      ),
    },
  ];

  const onToggleModalPreview = async () => {
    await getPromptData();
    setShowModalPreviewPrompt(!isShowModalPreviewPrompt);
  };

  return (
    <div className="instruction-detail">
      <Tabs
        onChange={onChangeTab}
        defaultActiveKey="1"
        className={clsx("instruction-detail__tabs", "tabs", { "tab-is-hidden": !id })}
        items={[...tabItems]}
        tabBarExtraContent={<AntButton
          type={BUTTON.GHOST_BLUE}
          size="small"
          onClick={onToggleModalPreview}
        >
          {t("PREVIEW_PROMPT_INSTRUCTION")}
        </AntButton>}
      />
      <PreviewPromptInstruction
        visible={isShowModalPreviewPrompt}
        promptData={promptData}
        onCancel={() => setShowModalPreviewPrompt(false)}
      />
    </div>
  );
};

export default InstructionDetail;
