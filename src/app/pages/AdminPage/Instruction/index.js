import React, { useEffect, useState } from "react";
import { Card, Col, Form, Input, Row, Select, Tag, Tooltip } from "antd";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { CheckOutlined, EditOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";

import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import { copyInstruction, deleteInstruction, getPaginationInstruction } from "@services/Instruction";
import { handlePagingData } from "@common/dataConverter";
import { handleReplaceUrlSearch, handleSearchParams, orderColumn, paginationConfig } from "@common/functionCommons";
import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import { BUTTON, OUTPUT_TYPE, PAGINATION_INIT, RESPONSE_FORMAT } from "@constant";
import { LINK } from "@link";

import "./Instruction.scss";
import { getGptModel } from "@src/app/services/GPTModelPrice";
import Copy from "@component/SvgIcons/Copy";
import { getOutputTypes } from "@services/OutputType";



const Instruction = () => {
  const [instructionData, setInstructionData] = useState(PAGINATION_INIT);
  const [outputTypeData, setOutputTypeData] = useState([]);
  const [gptModelData, setGPTModelData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [formFilter] = Form.useForm();
  useEffect(() => {
    Promise.all([
      getGptModelData(),
      getOutputTypeData(),
    ]);
  }, []);

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getInstructionData(paging, query);
  }, [location.search]);

  async function getOutputTypeData() {
    const dataResponse = await getOutputTypes();
    if (dataResponse) {
      setOutputTypeData(dataResponse);
    }
  }

  const getInstructionData = async (paging = instructionData.paging, query = instructionData.query) => {
    setIsLoading(true);
    const dataResponse = await getPaginationInstruction(paging, query);
    if (dataResponse) {
      setInstructionData(handlePagingData(dataResponse, query));
    }
    setIsLoading(false);
  };

  async function getGptModelData() {
    const apiResponse = await getGptModel();
    if (apiResponse) {
      setGPTModelData(apiResponse);
    }
  }

  const handleDelete = (instructionId, instructionName) => {
    confirm.delete({
      title: t("DELETE_INSTRUCTION"),
      content: t("DELETE_INSTRUCTION_CONFIRM", { name: instructionName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setIsLoading(true);
        const apiResponse = await deleteInstruction(instructionId, true);
        if (apiResponse) {
          toast.success(t("DELETE_INSTRUCTION_SUCCESS"));
          await getInstructionData();
        } else {
          toast.error(t("DELETE_INSTRUCTION_ERROR"));
          setIsLoading(false);
        }
      },
    });
  };
  const handleCopy = async (instructionId, instructionName) => {
    try {
      setIsLoading(true);
      const apiResponse = await copyInstruction({ instructionId }, true);
      if (!apiResponse) throw new Error("Copy failed");

      toast.success(t("COPY_INSTRUCTION_SUCCESS", { name: instructionName }));

      const paging = { ...instructionData.paging, page: 1 };

      handleReplaceUrlSearch(1, paging.pageSize, {});
      await getInstructionData(paging);
    } catch (error) {
      console.error("Failed to copy instruction:", error);
      toast.error(t("COPY_INSTRUCTION_ERROR"));
      setIsLoading(false);
    }
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, instructionData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, instructionData.paging.pageSize, {});
  };

  const columns = [
    {
      ...orderColumn(instructionData.paging),
      width: 120,
    },
    {
      title: t("NAME"),
      dataIndex: "shortName",
      width: 200,
      render: (text) => <span className="instruction-name-value">{text}</span>,
    },
    {
      title: t("GPT_MODEL"),
      dataIndex: "gptModel",
      width: 150,
      render: (text) => {
        if (!text) return "-";
        return <span className="gpt-model-value">{text}</span>;
      },
    },
    {
      title: t("CHAT_TYPE"),
      dataIndex: "chatType",
      width: 150,
      render: (text) => {
        let color;
        switch (text) {
          case "text":
            color = "blue";
            break;
          case "image":
            color = "orange";
            break;
          case "chat":
            color = "green";
            break;
          case "completion":
            color = "purple";
            break;
          case "function_calling":
            color = "magenta";
            break;
          case "vision":
            color = "cyan";
            break;
          default:
            color = "default";
        }
        return <Tag color={color}>{text}</Tag>;
      },
    },
    //{
    //  title: "Temperature",
    //  dataIndex: "temperature",
    //  width: 150,
    //},
    {
      title: t("OUTPUT_TYPE"),
      dataIndex: "outputTypeId",
      width: 150,
      render: (value) => {
        if (!value?.name) return "-";
        return <span className="output-type-value">{value.name}</span>;
      },
    },
    {
      title: t("RESPONSE_FORMAT"),
      dataIndex: "outputTypeId",
      width: 150,
      render: (value) => {
        if (!value?.responseFormat) return "-";
        let color;
        switch (value.responseFormat) {
          case "json":
            color = "blue";
            break;
          case "text":
            color = "green";
            break;
          case "html":
            color = "purple";
            break;
          case "markdown":
            color = "cyan";
            break;
          case "json_object":
            color = "geekblue";
            break;
          default:
            color = "default";
        }
        return <Tag color={color}>{value.responseFormat}</Tag>;
      },
    },
    {
      title: t("IS_SUB_INSTRUCTION"),
      dataIndex: "isSubInstruction",
      width: 150,
      align: "center",
      render: (value) => {
        if (value) {
          return <Tag color="green">{t("YES")}</Tag>;
        } else {
          return <Tag color="default">{t("NO")}</Tag>;
        }
      },
    },

    // {
    //   title: "Instruction",
    //   dataIndex: "instruction",
    //   width: 500,
    // },
    {
      title: t("ACTION"),
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className="instruction-actions">
          <Tooltip title={t("COPY_INSTRUCTION")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-copy-instruction"}
              icon={<Copy/>}
              onClick={() => handleCopy(record?._id, record?.shortName)}
            />
          </Tooltip>
          <Tooltip title={t("EDIT_INSTRUCTION")}>
            <Link to={LINK.INSTRUCTION_DETAIL.format(record._id)}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className={"btn-edit-instruction"}
                icon={<EditOutlined/>}
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_INSTRUCTION")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-instruction"}
              icon={<DeleteIcon/>}
              onClick={() => handleDelete(record?._id, record?.shortName)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(instructionData.paging, instructionData.query, i18n.language);
  const gptModelOptions = gptModelData.map((item) => ({ label: item.gptModel, value: item.gptModel }));

  return (
    <Loading active={isLoading} transparent>
      <div className="instruction">
        <Card className="instruction-info-card">
          <div className="instruction-info-header">
            <div>
              <h1 className="instruction-title">{t("INSTRUCTION_MANAGEMENT")}</h1>
              <p className="instruction-description">{t("INSTRUCTION_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.INSTRUCTION_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create-instruction"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_INSTRUCTION")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="instruction-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="shortName" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_INSTRUCTION_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="gptModel" className="search-form-item">
                      <Select
                        options={gptModelOptions}
                        allowClear
                        placeholder={t("FILTER_BY_GPT_MODEL")}
                        showSearch
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="outputTypeId" className="search-form-item">
                      <Select
                        options={outputTypeData}
                        placeholder={t("FILTER_BY_OUTPUT_TYPE")}
                        allowClear
                        fieldNames={{ label: "name", value: "_id" }}
                        optionFilterProp="name"
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="instruction-table-card">
          <TableAdmin
            columns={columns}
            dataSource={instructionData.rows}
            pagination={{ ...pagination }}
            scroll={{ x: 1000 }}
            className={"instruction-table"}
            rowClassName={() => "instruction-table-row"}
            locale={{ emptyText: t("NO_INSTRUCTIONS_FOUND") }}
          />
        </Card>
      </div>
    </Loading>
  );
};

export default Instruction;
