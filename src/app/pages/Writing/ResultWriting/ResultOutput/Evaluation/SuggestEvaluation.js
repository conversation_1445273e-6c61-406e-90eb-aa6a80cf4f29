import { CONSTANT } from "@constant";
import clsx from "clsx";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from 'react-i18next';
import { WRITING_EVALUATION_LIST } from "./functionCommon";

const SuggestEvaluation = (props) => {
  const { t } = useTranslation();
  const { suggests, assignment, setAssignment, evaluationSelected } = props;

  const activeSuggestIndexRef = useRef(-1);
  const suggestDataRef = useRef([]);

  const [suggestData, setSuggestData] = useState([]);

  useEffect(() => {
    if (suggests?.length) setSuggestData(suggests);
  }, [suggests]);

  useEffect(() => {
    activeSuggestIndexRef.current = suggestData.findIndex(item => item.isActive);
    suggestDataRef.current = suggestData;
  }, [suggestData]);

  //handle onBlur suggest item
  useEffect(() => {
    const handleClick = (event) => {
      if (activeSuggestIndexRef.current === -1) return;
      const isBlurAllSuggest = event.target.closest('.suggest-item') === null;

      if (isBlurAllSuggest) {
        const oldSelectText = suggestDataRef.current[activeSuggestIndexRef.current]?.text;
        setSuggestData(pre => pre.map((item, idx) => idx === activeSuggestIndexRef.current
          ? { ...item, isActive: false }
          : item));
        setAssignment(pre => removeHighlightSubstring(oldSelectText, pre));
      }
    }

    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, []);

  //find all string in text
  const findStrInText = (fintStr, text) => {
    const regexTarget = escapeRegExp(fintStr).split(' ').join('\\s*(<br>\\s*)?');
    const regex = new RegExp(regexTarget, 'gi');

    // Tìm kiếm các chuỗi khớp
    const matches = [];
    let match;
    while ((match = regex.exec(text)) !== null) {
      matches.push(match[0]);
    }
    return matches;
  };

  //xử lý các ký tự đặc biệt trong string
  const escapeRegExp = string => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  //add highlight text
  const highlightSubstring = (searchText = '', inputText = '') => {
    if (!searchText) return inputText;

    // Tạo regex không phân biệt chữ hoa, chữ thường và khớp với các dấu phân tách
    const regexTarget = escapeRegExp(searchText).split(' ').join('\\s*(<br>\\s*)?');

    const regex = new RegExp(regexTarget, 'gi');

    const replacer = (match) => {
      return `<span style="background-color:#0C93FF33">${match}</span>`;
    };

    return inputText.replace(regex, replacer);
  }

  //remove highlight text
  const removeHighlightSubstring = (searchText = '', inputText = '') => {
    if (!searchText) return inputText;

    const matches = findStrInText(searchText, inputText);

    if (matches.length === 0) return inputText;

    matches.forEach(match => {
      inputText = inputText.replaceAll(`<span style="background-color:#0C93FF33">${match}</span>`, match);
    })
    return inputText;
  }

  const changeHighlightToBold = (searchText = '', replaceText = '', inputText = '') => {
    if (!searchText || !replaceText) return inputText;

    const matches = findStrInText(searchText, inputText);

    if (matches.length === 0) return inputText;

    matches.forEach(match => {
      let replacement = replaceText;
      if (match[0] === match[0].toUpperCase()) {
        replacement = replacement.charAt(0).toUpperCase() + replacement.slice(1);
      }
      inputText = inputText.replaceAll(`<span style="background-color:#0C93FF33">${match}</span>`, `<b>${replacement}</b>`);
    })
    return inputText;
  }

  const onSelectSuggest = (index) => {
    const selectSuggest = suggestDataRef.current[index];
    if (selectSuggest.isActive || selectSuggest.isApplied) return;

    const newSelectText = selectSuggest?.text;
    let newEssayText = highlightSubstring(newSelectText, assignment);

    if (activeSuggestIndexRef.current !== -1) {
      const oldSelectText = suggestDataRef.current[activeSuggestIndexRef.current]?.text;
      newEssayText = removeHighlightSubstring(oldSelectText, newEssayText);
    }

    setAssignment(newEssayText);

    // update active index for suggest data
    setSuggestData((pre) => pre.map((item, idx) => idx === index
      ? { ...item, isActive: true }
      : { ...item, isActive: false }));
  }

  const onApplySuggest = (index) => {
    const applySuggest = suggestDataRef.current[index];
    const { text, suggest, isActive } = applySuggest || {};
    if (!isActive) return;

    const updateAssignment = changeHighlightToBold(text, suggest, assignment);
    setAssignment(updateAssignment);

    //apply suggest and clear active for this index in suggest data
    setSuggestData((pre) => pre.map((item, idx) => idx === index
      ? { ...item, isApplied: true, isActive: false }
      : item));
  }

  if ((!suggestData?.filter(suggest => !suggest?.isApplied)?.length)
    || !['suggests', CONSTANT.ALL].includes(evaluationSelected)) return null;

  return <div className="evaluation__content__item evaluation-suggest">
    <div
      className="content__item__title evaluation-suggest__title"
      style={{ color: WRITING_EVALUATION_LIST.suggests.color }}
    >
      <img src={WRITING_EVALUATION_LIST.suggests.icon} alt="" />
      {t("UPGRADE_GRAMMAR_AND_VOCABULARY")}
    </div>

    {suggestData.map((item, index) => {
      const { isActive, isApplied } = item;
      if (isApplied) return null;
      return (<div key={index}
        id={`suggest-item-${index}`}
        className={clsx('suggest-item', { 'suggest-item-active': isActive })}
        onClick={() => onSelectSuggest(index)}
      >
        <span className="suggest-item__text">
          <span>{item.text}</span>
          <span className="suggest-item__arrow">{` -> `}</span>
          <span
            className="suggest-item__suggestion"
            onClick={() => onApplySuggest(index)}>
            {`${item.suggest}`}
          </span>
        </span>

        <span className="suggest-item__explanation">
          {`Explanation:  ${item.explanation}`}
        </span>
      </div>)
    })
    }
  </div>
}


export default SuggestEvaluation;