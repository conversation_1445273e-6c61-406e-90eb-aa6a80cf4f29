import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from "react-i18next";
import { connect } from 'react-redux';
import clsx from 'clsx';

import AntModal from '@src/app/component/AntModal';
import Loading from '@src/app/component/Loading';
import AntButton from "@component/AntButton";

import { BUTTON, UPLOAD_STATUS } from '@constant';

import { getImageDetail } from "@services/Image";
import { getFileById } from "@services/File";
import { toast } from "@component/ToastProvider";

import SelectFile from './SelectFile';
import PreviewImage from './PreviewImage';

import './ModalUploadFile.scss';
import PreviewPdf from './PreviewFileEssay';

const ModalUploadFile = ({ availableWorkspaces, ...props }) => {
  const { t } = useTranslation();
  const { open, onClose } = props;
  const { imageEssayIds, setImageEssayIds } = props;
  const { fileEssayId, setFileEssayId } = props;
  const { pageRange, setPageRange, setTotalPages: setOldTotalPages } = props;

  const workspaceId = availableWorkspaces?.[0]?._id || null;

  const [imagesDisplay, setImagesDisplay] = useState([]);
  const [pdfDisplay, setPdfDisplay] = useState(null);
  const [pageRangeDisplay, setPageRangeDisplay] = useState({ startPage: 0, endPage: 0 });
  const [totalPages, setTotalPages] = useState(0);
  const [uploadedPdfId, setUploadedPdfId] = useState("");

  const [filesUploadStatus, setFilesUploadStatus] = useState({});

  const [isLoadingModal, setLoadingModal] = useState(false);

  useEffect(() => {
    if (open) {
      setPageRangeDisplay({ startPage: 0, endPage: 0 });
      setImagesDisplay([]);
      setPdfDisplay(null);
      setUploadedPdfId("");
      setTotalPages(0);

      if (imageEssayIds.length) {
        getImagesData();
      } else if (fileEssayId) {
        getPdfData();
      }
    }
  }, [open]);

  const isDisableSave = useMemo(() => {
    if (pdfDisplay) {
      return [UPLOAD_STATUS.ERROR, UPLOAD_STATUS.UPLOADING].includes(filesUploadStatus?.status);
    } else {
      return Object.values(filesUploadStatus)
        .some(item => [UPLOAD_STATUS.ERROR, UPLOAD_STATUS.UPLOADING].includes(item.status));
    }
  }, [filesUploadStatus]);

  const getImagesData = async () => {
    setLoadingModal(true);
    const allRequest = imageEssayIds.map(id => getImageDetail(id));
    const responses = await Promise.all(allRequest);
    if (responses) {
      setImagesDisplay(responses.map(item => ({ data: item, key: item?._id })));
    }
    setLoadingModal(false);
  }

  const getPdfData = async () => {
    setLoadingModal(true);
    const response = await getFileById(fileEssayId);
    if (response) {
      setPdfDisplay(response);
      setPageRangeDisplay(pageRange);
    }
    setLoadingModal(false);
  }

  const validatePageRange = useMemo(() => {
    const { startPage, endPage } = pageRangeDisplay;
    if (!startPage || !endPage || startPage < 1 || endPage > totalPages || startPage > endPage) {
      return { isError: true, errorMsg: t("PAGE_NUMBER_INVALID") };
    } else if (endPage - startPage >= 2) {
      return { isError: true, errorMsg: t("PAGE_NUMBER_LIMIT") };
    }
    return { isError: false, errorMsg: "" };
  }, [pageRangeDisplay, totalPages]);

  const validateInputType = () => {
    if (!pdfDisplay && !imagesDisplay.length) {
      toast.warning({ description: t("NOT_UPLOAD_FILE_WARNING") });
      return false;
    }
    if (pdfDisplay) {
      return !validatePageRange.isError;
    }
    return true;
  }

  async function handleSave() {
    const validateFile = validateInputType();
    if (!validateFile) return;

    uploadedPdfId && setFileEssayId(uploadedPdfId);
    setPageRange(pageRangeDisplay);
    setOldTotalPages(totalPages);
    setImageEssayIds(imagesDisplay.map(item => item?.data?._id));
    onClose();
  }

  return (
    <AntModal
      footerless
      className="writing-upload-file-modal"
      open={open}
      onCancel={onClose}
      title={t("UPLOAD_FILE")}
      confirmLoading={isLoadingModal}
    >
      <Loading active={isLoadingModal}>
        <SelectFile
          imagesDisplay={imagesDisplay}
          setImagesDisplay={setImagesDisplay}
          pdfDisplay={pdfDisplay}
          setPdfDisplay={setPdfDisplay}
          setPageRangeDisplay={setPageRangeDisplay}
          setUploadedPdfId={setUploadedPdfId}
          setFilesUploadStatus={setFilesUploadStatus}
          workspaceId={workspaceId}
        />

        {(imagesDisplay.length || pdfDisplay) && <span className='preview-file-title'>{t("UPLOADED_FILE")}</span>}

        <PreviewImage
          imagesData={imagesDisplay}
          setImagesData={setImagesDisplay}
          filesUploadStatus={filesUploadStatus}
          setFilesUploadStatus={setFilesUploadStatus}
        />

        <PreviewPdf
          pdfFile={pdfDisplay}
          pageRange={pageRangeDisplay}
          setPageRange={setPageRangeDisplay}
          validatePageRange={validatePageRange}
          totalPages={totalPages}
          setTotalPages={setTotalPages}
          filesUploadStatus={filesUploadStatus}
          setFilesUploadStatus={setFilesUploadStatus}
        />
      </Loading>
      <div className="footer-action">
        <AntButton
          size="large"
          onClick={onClose}
          type={BUTTON.WHITE}
        >
          {t("CANCEL")}
        </AntButton>
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={handleSave}
          disabled={isLoadingModal || isDisableSave}
        >
          {t('SAVE_FILE')}
        </AntButton>
      </div>
    </AntModal>
  );
};

const mapStateToProps = (store) => {
  const { availableWorkspaces } = store.workspace;
  return { availableWorkspaces };
};
export default (connect(mapStateToProps)(ModalUploadFile));