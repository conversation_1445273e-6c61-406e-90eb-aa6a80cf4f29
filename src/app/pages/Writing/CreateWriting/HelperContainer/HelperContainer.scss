@import 'src/app/styles/scroll';
@import 'src/app/styles/markdown-helper';
.helper-container {
  display: flex;
  flex-direction: column;
  width: 248px;
  margin-left: 12px;
  border-radius: 24px;
  background-color: #ffffff;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-in-out;
  max-height: calc(100vh - 105px);
  overflow: hidden;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  // Common styles for content in generate-idea and find-vocab modes
  &--generate-idea,
  &--find-vocab {
    .helper-container__content {
      height: auto;
      hr {
        margin: 8px 0 16px;
      }
    }
  }

  // Specific styles for generate-idea mode content
  &--generate-idea {
    .helper-container__content {
      overflow: hidden;
    }
  }

  &__header {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 16px;
    gap: 4px;
    color: #09196b;

    img.gen-icon {
      width: 24px;
      height: 24px;
    }

    p {
      font-family: 'Inter', sans-serif;
      font-weight: 600;
      font-size: 22px;
      line-height: 1.36em;
      color: #09196b;
      margin: 0;
    }
  }

  &__tabs {
    display: flex;
    flex-direction: row;
    padding: 0px 16px;
    margin-bottom: 8px;

    &-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    &-item {
      flex: 0 1 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 2px 4px;
      border-radius: 4px;
      cursor: pointer;

      p {
        font-family: 'Inter', sans-serif;
        font-weight: 400;
        font-size: 16px;
        line-height: 1.5em;
        color: #09196b;
        margin: 0;
      }

      &--active {
        background-color: #09196b;

        p {
          color: #ffffff;
        }
      }
    }
  }

  &__content {
    padding: 0px 16px 16px;
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.5em;
    color: #000000;
    text-align: left;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    overflow-x: visible;
    scroll-behavior: smooth;
    word-wrap: break-word;
    white-space: normal;

    hr {
      border: none;
      height: 1px;
      background-color: #b3b3b3;
      margin: 8px 0;
    }
  }

  &__collapse {
    display: flex;
    margin-top: 16px;
    margin-left: -12px;
    justify-content: center;
    align-items: center;
    position: absolute;

    &-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 26px;
      height: 26px;
      background-color: #ffffff;
      border-radius: 4px;
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
      }

      .double-arrow-icon {
        width: 14px;
        height: 14px;
        display: block;
      }
    }
  }
}

// CSS cho VocabularyContent
.vocabulary-content {
  display: flex;
  flex-direction: column;

  &__header {
    margin-bottom: 16px;
  }

  &__label {
    font-size: 16px;
    font-weight: 500;
    color: #09196b;
    margin-top: 0;
  }

  &__select {
    width: 100%;
    padding-left: 2px;
    padding-right: 2px;
  }

  &__find-button {
    width: 135px !important;
    align-self: center;
  }

  .ant-btn {
    border-radius: 12px !important;
    opacity: 1;
  }

  &__loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex-grow: 1;
    color: #555;
  }

  &__message-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 16px;
    text-align: center;
  }

  &__message-image {
    width: 100px;
    height: auto;
    filter: drop-shadow(0px 4px 20px rgba(32, 0, 255, 0.4));
  }

  &__message {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 1.3em;
    color: #09196B;
    text-align: center;
  }

  &__list {
    flex-grow: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 8px;
    margin-top: 8px;
    scroll-behavior: smooth;
    @include scrollbar;
  }

  &__item {
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }

  &__word-info {
    display: flex;
    align-items: baseline;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 4px;
  }

  &__word {
    color: #000;
    font-size: 1.05em;
  }

  &__type {
    font-size: 0.85em;
    color: #000;
  }

  &__pronunciation {
    font-size: 0.9em;
    color: #000;
  }

  &__meaning {
    font-size: 0.95em;
    color: #000;
    margin-bottom: 4px;
    font-weight: 600;
  }

  &__example {
    font-size: 0.9em;
    color: #000;
    font-style: italic;

    &-label {
      font-weight: 500;
      margin-right: 4px;
      color: #000;
      font-style: normal;
    }
  }
}

.floating-help-button {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 4px 8px;
  background-color: #e7e5ff;
  border-radius: 8px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 1;
  transform: translateY(0);
  animation: floatIn 0.3s ease-out;

  @keyframes floatIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &:hover {
    background-color: #d6d3ff;
    transform: translateY(-2px);
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
  }

  &__text {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.3em;
    color: #3a18ce;
    text-align: right;
    margin: 0;
    white-space: nowrap;
  }

  .remove-icon {
    width: 14px;
    height: 14px;
  }

  // Mode Generate Idea
  &--generate-idea {
    background-color: #e8f5ff;

    &:hover {
      background-color: #d4edff;
    }

    .floating-help-button__text {
      color: #0064c0;
    }
  }

  // Mode Find Vocab
  &--find-vocab {
    background-color: #fce4fb;

    &:hover {
      background-color: #fad6f9;
    }

    .floating-help-button__text {
      color: #a5008c;
    }
  }
}

.generated-idea-content, .generated-understanding-content, .vocabulary-content {
  @include markdown-base-styles;
}

.generated-idea-content, .generated-understanding-content, .vocabulary-content__list {
  overflow-y: auto;
  scroll-behavior: smooth;
  padding-right: 8px;

  // Tùy chỉnh thanh cuộn cho WebKit - luôn hiển thị và mỏng
  &::-webkit-scrollbar {
    display: block; // Đảm bảo thanh cuộn luôn hiển thị để chiếm không gian
    width: 8px;    // Độ rộng của thanh cuộn
    height: 6px;   // Chiều cao (cho thanh cuộn ngang, nếu có)
  }

  &::-webkit-scrollbar-thumb {
    background-color: #aaa; // Màu của con trượt
    border-radius: 20px;    // Bo góc con trượt

    &:hover {
      background-color: #999; // Màu con trượt khi hover
    }
  }

  // Tùy chỉnh thanh cuộn cho Firefox
  @supports (-moz-appearance:none) {
    scrollbar-width: thin; // Làm mỏng thanh cuộn
    // overflow-y: auto; đã được đặt ở trên
  }
}
