import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import './HelperContainer.scss';

const HelperContainer = ({
  activeTab,
  helperContent,
  handleTabChange,
  toggleHelperCollapse,
  aiGenStarIcon,
  doubleArrowIcon,
  mode = 'normal',
  title,
  isTask1 = false,
}) => {
  const { t } = useTranslation();

  const isGenerateIdeaMode = mode === 'generate-idea';
  const isFindVocabMode = mode === 'find-vocab';

  return (
    <div className={`helper-container ${isGenerateIdeaMode ? 'helper-container--generate-idea' : ''} ${isFindVocabMode ? 'helper-container--find-vocab' : ''}`}>
      <div className="helper-container__header">
        <img src={aiGenStarIcon} alt={t('AI_HELP')} className="gen-icon" />
        <p>{title || (isGenerateIdeaMode ? t('GENERATE_IDEA') : isFindVocabMode ? t('FIND_VOCAB') : t('HELP_ME_WRITE'))}</p>
      </div>

      {!isGenerateIdeaMode && !isFindVocabMode && (
        <div className="helper-container__tabs">
          <div className="helper-container__tabs-list">
            <div
              className={`helper-container__tabs-item ${activeTab === 'topic' ? 'helper-container__tabs-item--active' : ''}`}
              onClick={() => handleTabChange('topic')}
            >
              <p>{t('TOPIC')}</p>
            </div>
            <div
              className={`helper-container__tabs-item ${activeTab === 'introduction' ? 'helper-container__tabs-item--active' : ''}`}
              onClick={() => handleTabChange('introduction')}
            >
              <p>{t('INTRODUCTION')}</p>
            </div>
            {isTask1 && (
              <div
                className={`helper-container__tabs-item ${activeTab === 'overview' ? 'helper-container__tabs-item--active' : ''}`}
                onClick={() => handleTabChange('overview')}
              >
                <p>{t('OVERVIEW')}</p>
              </div>
            )}
            <div
              className={`helper-container__tabs-item ${activeTab === 'body' ? 'helper-container__tabs-item--active' : ''}`}
              onClick={() => handleTabChange('body')}
            >
              <p>{t('BODY_PARAGRAPHS')}</p>
            </div>
            {!isTask1 && (
              <div
                className={`helper-container__tabs-item ${activeTab === 'conclusion' ? 'helper-container__tabs-item--active' : ''}`}
                onClick={() => handleTabChange('conclusion')}
              >
                <p>{t('CONCLUSION')}</p>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="helper-container__content">
        <hr />
        {helperContent}
      </div>

      <div className="helper-container__collapse">
        <div
          className="helper-container__collapse-button"
          onClick={toggleHelperCollapse}
          aria-label={t('COLLAPSE_HELPER_PANEL')}
        >
          <img src={doubleArrowIcon} alt={t('COLLAPSE')} className="double-arrow-icon" />
        </div>
      </div>
    </div>
  );
};

HelperContainer.propTypes = {
  activeTab: PropTypes.string,
  helperContent: PropTypes.node.isRequired,
  handleTabChange: PropTypes.func,
  toggleHelperCollapse: PropTypes.func.isRequired,
  aiGenStarIcon: PropTypes.string.isRequired,
  doubleArrowIcon: PropTypes.string.isRequired,
  mode: PropTypes.oneOf(['normal', 'generate-idea', 'find-vocab']),
  title: PropTypes.string,
  isTask1: PropTypes.bool,
};

export default HelperContainer;
