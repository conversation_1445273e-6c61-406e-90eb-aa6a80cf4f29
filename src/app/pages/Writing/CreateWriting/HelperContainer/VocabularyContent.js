import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Spin } from 'antd';
import './HelperContainer.scss';
import { useTranslation } from 'react-i18next';
import { findVocabulary } from '@src/app/services/Writing';
import { TASK_TYPES } from '@src/constants';
import AntButton from '@src/app/component/AntButton';
import { SelectCommon } from '@src/app/component';
import { BUTTON } from '@constant';
import noTopicImage from '@src/asset/image/no-topic.png';

const VocabularyContent = ({ topic, isTask1, imageTopicId }) => {
  const { t } = useTranslation();
  const [level, setLevel] = useState('moderate');
  const [vocabularies, setVocabularies] = useState([]);
  const [isLoadingVocab, setIsLoadingVocab] = useState(false);
  const [vocabCache, setVocabCache] = useState({});

  console.log('topic', topic, 'vocabularies', vocabularies);

  useEffect(() => {
    setVocabCache({});
    setVocabularies([]);
  }, [topic, isTask1, imageTopicId]);

  const handleLevelChange = (value) => {
    setLevel(value);
    if (vocabCache[value]) {
      setVocabularies(vocabCache[value]);
    } else {
      setVocabularies([]);
    }
  };

  const handleFindVocabClick = async () => {
    if (vocabCache[level]) {
      setVocabularies(vocabCache[level]);
      return;
    }

    setIsLoadingVocab(true);
    setVocabularies([]);

    try {
      if (!topic || !topic.trim()) {
        const messageData = [{ type: 'message', message: t('TOPIC_EMPTY_HELPER_MESSAGE_VOCAB') }];
        setVocabularies(messageData);
        setIsLoadingVocab(false);
        return;
      }

      const data = {
        topic: topic,
        taskType: isTask1 ? TASK_TYPES.TASK_1 : TASK_TYPES.TASK_2,
        difficultyLevel: level,
        ...(isTask1 && imageTopicId ? { topicImageId: imageTopicId } : {})
      };

      const response = await findVocabulary(data);

      let resultData;
      if (response && response.vocabularies && response.vocabularies.length > 0) {
        resultData = response.vocabularies;
        setVocabularies(resultData);
      } else {
        resultData = [{ type: 'message', message: t('EMPTY_VOCAB_MESSAGE') }];
        setVocabularies(resultData);
      }
      setVocabCache(prev => ({ ...prev, [level]: resultData }));
    } catch (error) {
      console.error('Error finding vocabulary:', error);
      const errorData = [{ type: 'message', message: t('ERROR_FINDING_VOCAB') }];
      setVocabularies(errorData);
    } finally {
      setIsLoadingVocab(false);
    }
  };

  return (
    <div className="vocabulary-content">
      <div className="vocabulary-content__header">
        <p className="vocabulary-content__label">{t('DIFFICULTLY_LEVEL')}</p>
        <SelectCommon
          defaultValue="moderate"
          value={level}
          onChange={handleLevelChange}
          options={[
            { value: 'easy', label: t('EASY') },
            { value: 'moderate', label: t('MODERATE') },
            { value: 'hard', label: t('HARD') },
            { value: 'expert', label: t('EXPERT') },
          ]}
          fieldNames={{ label: 'label', value: 'value' }}
          className="vocabulary-content__select"
          disabled={isLoadingVocab}
        />
      </div>

      <AntButton
        className="vocabulary-content__find-button"
        type={BUTTON.DEEP_NAVY}
        size="large"
        onClick={handleFindVocabClick}
        loading={isLoadingVocab}
        block
      >
        {t('FIND_VOCAB')}
      </AntButton>

      {isLoadingVocab ? (
        <div className="vocabulary-content__loading">
          {t('LOADING_VOCAB')}
        </div>
      ) : (
        <div className="vocabulary-content__list" style={{ display: vocabularies.length > 0 ? 'block' : 'none' }}>
          {vocabularies.map((item, index) => {
            if (item.type === 'message') {
              return (
                <div key={index} className="vocabulary-content__message-container">
                  <img
                    src={noTopicImage}
                    alt={t('NO_TOPIC')}
                    className="vocabulary-content__message-image"
                  />
                  <div className="vocabulary-content__message">
                    {item.message}
                  </div>
                </div>
              );
            }
            return (
              <div key={index} className="vocabulary-content__item">
                <div className="vocabulary-content__word-info">
                  <span className="vocabulary-content__word">{item.word}</span>
                  {item.type && <span className="vocabulary-content__type">({item.type})</span>}
                  {item.ipa && <span className="vocabulary-content__pronunciation">{item.ipa}</span>}
                </div>
                {item.meaning && <div className="vocabulary-content__meaning">{item.meaning}</div>}
                {item.example && (
                  <div className="vocabulary-content__example">
                    <span className="vocabulary-content__example-label">{t('EXAMPLE_LABEL')}</span>
                    {item.example}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

VocabularyContent.propTypes = {
  topic: PropTypes.string,
  isTask1: PropTypes.bool,
  imageTopicId: PropTypes.string,
};

export default VocabularyContent;
