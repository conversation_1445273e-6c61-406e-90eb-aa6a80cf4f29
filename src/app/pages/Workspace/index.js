import React, { useContext, useEffect, useMemo, useState } from "react";
import { useLocation } from "react-router-dom";
import { connect } from "react-redux";
import { Select, Popover } from "antd";
import { debounce } from "lodash";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import TableLayout from "./TableLayout";
import GridLayout from "./GridLayout";
import WorkspaceInfo from "@component/WorkspaceInfo";
import WorkspaceCreateNew from "@src/app/component/WorkspaceCreateNew";
import Loading from "@component/Loading";
import AntButton from "@component/AntButton";
import SearchInput from "@component/SearchInput";
import NoData from "@component/NoData";
import Move from "@component/Move";
import Share from "@component/Share";
import RenameDescription from "@component/RenameDescription";

import { LINK } from "@link";
import { BUTTON, CONSTANT, LAYOUT_TYPE, WORKSPACE_TYPE } from "@constant";

import { getWorkspace<PERSON>elongs } from "@services/Workspace";
import { usePageViewTracker } from "@src/ga";

import GRID_LAYOUT_ICON from "@src/asset/icon/button-newui/grid-layout-icon.svg";
import TABLE_LAYOUT_ICON from "@src/asset/icon/button-newui/table-layout-icon.svg";
import Info from "@component/SvgIcons/InfoCircle/Info14";

import "./Workspace.scss";

import * as workspaceRedux from "@src/ducks/workspace.duck";

import { copyProject, deleteProject, updateProject } from "@services/Project";
import { copyFolder, deleteFolder, updateFolder } from "@services/Folder";
import { saved, unsaved } from "@src/app/services/MySaved";
import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";

export const WorkspaceContext = React.createContext();

function Workspace({ user, availableWorkspaces, ...props }) {
  usePageViewTracker("Workspace");
  
  const { t } = useTranslation();
  const { pathname } = useLocation();
  
  const [workspaceData, setWorkspaceData] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [nameSearch, setNameSearch] = useState("");
  const [typeSearch, setTypeSearch] = useState(CONSTANT.ALL);
  const [sortBy, setSortBy] = useState(CONSTANT.NEWEST);
  const [layoutType, setLayoutType] = useState(localStorage.getItem("workspaceViewMode") || LAYOUT_TYPE.TABLE);
  const [isShowInfo, setShowInfo] = useState(false);
  const [stateModalShare, setStateModalShare] = useState({
    visible: false,
    query: {},
    name: "",
    owner: null,
  });
  const [stateModalRename, setStateModalRename] = useState({
    visible: false,
    initialValue: "",
    placeholder: "",
  });
  const [stateModalMove, setStateModalMove] = useState({
    visible: false,
    projectId: null,
  });
  
  useEffect(() => {
    props.getAvailableWorkspaces();
  }, []);
  
  useEffect(() => {
    localStorage.setItem("workspaceViewMode", layoutType);
  }, [layoutType]);
  
  const workspaceDetail = useMemo(() => {
    if (!availableWorkspaces) {
      return null;
    }
    let type = WORKSPACE_TYPE.PERSONAL;
    if (pathname !== LINK.MY_WORKSPACE) {
      type = WORKSPACE_TYPE.ORGANIZATIONAL;
    }
    return availableWorkspaces.find((workspace) => workspace?.type === type);
  }, [pathname, availableWorkspaces]);
  
  const workspaceId = workspaceDetail?._id;
  
  const filteredData = useMemo(() => {
    if (nameSearch === "" && typeSearch === CONSTANT.ALL) return workspaceData;
    if (typeSearch === CONSTANT.FOLDER) {
      return workspaceData.filter((item) => item?.folderName && item?.folderName?.toLowerCase()?.includes(nameSearch?.toLowerCase()));
    }
    if (typeSearch === CONSTANT.PROJECT) {
      return workspaceData.filter((item) => item?.projectName && item?.projectName?.toLowerCase()?.includes(nameSearch?.toLowerCase()));
    }
    if (nameSearch !== "" && typeSearch === CONSTANT.ALL) {
      return workspaceData.filter(
        (item) => item?.projectName?.toLowerCase()?.includes(nameSearch?.toLowerCase()) || item?.folderName?.toLowerCase()?.includes(nameSearch?.toLowerCase()),
      );
    }
  }, [nameSearch, workspaceData, typeSearch]);
  
  const sortedData = useMemo(() => {
    const sortedArray = [...filteredData];
    sortedArray.sort((a, b) => {
      const dateA = new Date(a?.createdAt);
      const dateB = new Date(b?.createdAt);
      return sortBy === CONSTANT.NEWEST ? dateB - dateA : dateA - dateB;
    });
    return sortedArray;
  }, [filteredData, sortBy]);
  
  useEffect(() => {
    if (workspaceId) {
      getWorkspaceData();
    }
  }, [workspaceId]);
  
  const getWorkspaceData = async () => {
    setLoading(true);
    const dataResponse = await getWorkspaceBelongs(workspaceId);
    if (dataResponse) {
      setWorkspaceData(dataResponse);
    }
    setLoading(false);
  };
  
  const handleCopy = async (itemData) => {
    const isFolder = itemData?.folderName;
    const dataRequest = isFolder ? { folderId: itemData?._id } : { projectId: itemData?._id };
    const apiRequest = isFolder ? copyFolder : copyProject;
    const apiResponse = await apiRequest(dataRequest, true, { workspaceId });
    if (apiResponse) {
      if (apiResponse.workspaceId === workspaceId) {
        setWorkspaceData([{ ...apiResponse, ...isFolder ? { projects: itemData?.projects } : {} }, ...workspaceData]);
      }
      const newWorkspaces = availableWorkspaces.map(workspace => {
        if (workspace?._id === apiResponse?.workspaceId) {
          if (isFolder) return {
            ...workspace,
            folders: workspace.folders + 1,
            projects: workspace.projects + itemData.projects,
          };
          return { ...workspace, projects: workspace.projects + 1 };
        }
        return workspace;
      });
      props.setAvailableWorkspaces(newWorkspaces);
      const toMyWorkspace = apiResponse?.workspaceId !== workspaceId;
      const messageLang = toMyWorkspace ?
        (isFolder ? "COPY_FOLDER_TO_MY_WORSKSPACE_SUCCESS" : "COPY_PROJECT_TO_MY_WORSKSPACE_SUCCESS")
        : (isFolder ? "COPY_FOLDER_SUCCESS" : "COPY_PROJECT_SUCCESS");
      toast.success(messageLang);
    }
  };
  
  const handleRename = async (newName) => {
    const isFolder = stateModalRename?.isFolder;
    const dataRequest = { _id: stateModalRename?.id, [isFolder ? "folderName" : "projectName"]: newName };
    const apiRequest = isFolder ? updateFolder : updateProject;
    const apiResponse = await apiRequest(dataRequest, true, { workspaceId });
    if (apiResponse) {
      const newWorkspaceData = workspaceData.map((item) => {
        if (item?._id === apiResponse?._id) {
          return { ...item, ...apiResponse };
        }
        return item;
      });
      setWorkspaceData(newWorkspaceData);
      const messageLang = isFolder ? "UPDATE_FOLDER_SUCCESS" : "UPDATE_PROJECT_SUCCESS";
      toast.success(messageLang);
      toggleRename();
    }
  };
  
  const toggleRename = (data) => {
    const isFolder = data?.folderName;
    setStateModalRename({
      visible: !!data,
      initialValue: isFolder ? data?.folderName : data?.projectName,
      placeholder: isFolder ? t("FOLDER_NAME_PLACEHOLDER") : t("PROJECT_NAME_PLACEHOLDER"),
      id: data?._id,
      isFolder: !!data?.folderName,
    });
  };
  
  const handleDelete = async (data) => {
    confirm.delete({
      content: t("CONFIRM_DELETE_PROJECT"),
      handleConfirm: async () => {
        const isFolder = data?.folderName;
        const apiRequest = isFolder ? deleteFolder : deleteProject;
        const apiResponse = await apiRequest(data?._id, true, { workspaceId });
        if (apiResponse) {
          const newWorkspaces = availableWorkspaces.map(workspace => {
            if (workspace?._id === workspaceId) {
              if (isFolder) {
                return {
                  ...workspace,
                  folders: workspace.folders - 1,
                  projects: workspace.projects - data.projects,
                };
              }
              return { ...workspace, projects: workspace.projects - 1 };
            }
            return workspace;
          });
          props.setAvailableWorkspaces(newWorkspaces);
          const newData = workspaceData.filter((item) => item?._id !== data?._id);
          setWorkspaceData(newData);
          const messageLang = isFolder ? "DELETE_FOLDER_SUCCESS" : "DELETE_PROJECT_SUCCESS";
          toast.success(messageLang);
        }
      },
    });
  };
  
  const handleChangeStarred = async (data) => {
    const isFolder = data?.folderName;
    const { isSaved } = data;
    const apiRequest = !isSaved ? saved : unsaved;
    const apiResponse = isFolder ? await apiRequest(null, data?._id) : await apiRequest(data?._id, null);
    if (apiResponse) {
      const newWorkspaceData = workspaceData.map((item) => {
        if (item?._id === data._id) {
          return { ...item, isSaved: !item?.isSaved };
        }
        return item;
      });
      setWorkspaceData(newWorkspaceData);
    }
  };
  
  const toggleMove = (projectId) => {
    setStateModalMove({ projectId: projectId, visible: projectId ? true : false });
  };
  
  const handleAfterMove = (data) => {
    const newFolderId = data?.folderId?._id;
    let newData = [];
    workspaceData.forEach((item) => {
      if (item?._id !== data?._id) {
        if (item?._id === newFolderId) {
          newData.push({ ...item, projects: item?.projects + 1 });
        } else {
          newData.push(item);
        }
      }
    });
    setWorkspaceData(newData);
  };
  
  const toggleShare = (data) => {
    const isFolder = data?.folderName;
    const query = isFolder ? { folderId: data?._id } : { projectId: data?._id };
    setStateModalShare({
      visible: !!data,
      query: query,
      owner: data?.ownerId,
      name: isFolder ? data?.folderName : data?.projectName,
    });
  };
  
  const handleSearchChanges = (e) => {
    setNameSearch(e.target.value);
  };
  
  const debounceSearch = debounce(handleSearchChanges, 300);
  
  const handleChangeSelectSearch = (e) => {
    setTypeSearch(e);
  };
  
  const handleChangeLayoutType = () => {
    setLayoutType(layoutType === LAYOUT_TYPE.TABLE ? LAYOUT_TYPE.GRID : LAYOUT_TYPE.TABLE);
  };
  
  const onChangeSort = (value) => {
    setSortBy(value);
  };
  
  const observer = new MutationObserver((mutationsList, observer) => {
    // Duyệt qua danh sách các mutation
    for (const mutation of mutationsList) {
      // Kiểm tra xem có phải là mutation của thuộc tính className không
      if (mutation.type === "attributes" && mutation.attributeName === "class") {
        // Trigger sự kiện resize của window
        window.dispatchEvent(new Event("resize"));
        break; // Sau khi trigger sự kiện, thoát khỏi vòng lặp
      }
    }
  });
  
  useEffect(() => {
    const targetNode = document.getElementById("workspace-body");
    if (targetNode) {
      observer.observe(targetNode, { attributes: true });
    }
  }, [isLoading]);
  
  useEffect(() => {
    window.dispatchEvent(new Event("resize"));
  }, [sortedData]);
  
  if (!availableWorkspaces) {
    return null;
  }
  
  if (isLoading) {
    return <Loading active transparent />;
  }
  
  const layoutIcon = layoutType === LAYOUT_TYPE.TABLE ? GRID_LAYOUT_ICON : TABLE_LAYOUT_ICON;
  
  return (<WorkspaceContext.Provider
    value={{
      user,
      dataSource: sortedData,
      isShowInfo,
      handleCopy,
      toggleMove,
      toggleShare,
      toggleRename,
      handleDelete,
      handleChangeStarred,
    }}
  >
    <div className="workspace-container">
      <div className="workspace-header">
        <div className="workspace-header__filter">
          <SearchInput placeholder={t("SEARCH")} onChange={debounceSearch} allowClear />
          <Select
            value={typeSearch}
            size={"large"}
            onChange={handleChangeSelectSearch}
          >
            <Select.Option value={CONSTANT.ALL}>{t("FOLDER_AND_PROJECT")}</Select.Option>
            <Select.Option value={CONSTANT.FOLDER}>{t("FOLDER")}</Select.Option>
            <Select.Option value={CONSTANT.PROJECT}>{t("PROJECT")}</Select.Option>
          </Select>
          <Select
            value={sortBy}
            size={"large"}
            onChange={onChangeSort}
          >
            <Select.Option value={CONSTANT.OLDEST}>{t("SORT_OLDEST_TO_NEWEST")}</Select.Option>
            <Select.Option value={CONSTANT.NEWEST}>{t("SORT_NEWEST_TO_OLDEST")}</Select.Option>
          </Select>
        </div>
        <div className={"workspace-actions"}>
          <WorkspaceCreateNew workspaceId={workspaceId} />
          <Popover
            placement="bottomLeft"
            content={layoutType === LAYOUT_TYPE.TABLE ? t("GRID_LAYOUT") : t("TABLE_LAYOUT")}
            trigger="hover">
            <AntButton
              type={BUTTON.DEEP_NAVY}
              onClick={handleChangeLayoutType}
              size="large"
              icon={<img src={layoutIcon} alt="" />}
            />
          </Popover>
          <Popover
            placement="bottomLeft"
            content={t("SHOW_WORKSPACE_INFO")}
            trigger="hover">
            <AntButton
              className={clsx("workspace-actions__info-button", { "workspace-actions__info-button-active": isShowInfo })}
              type={BUTTON.DEEP_NAVY}
              size="large"
              icon={<Info />}
              onClick={() => setShowInfo(true)}
            />
          </Popover>
        
        </div>
      </div>
      <div id="workspace-body" className={clsx("workspace-body", { "workspace-body-show-info": isShowInfo })}>
        <div className="workspace-content">
          {sortedData?.length
            ? layoutType === LAYOUT_TYPE.GRID ? <GridLayout /> : <TableLayout />
            : <NoData />}
        </div>
        {isShowInfo && <WorkspaceInfo
          workspaceDetail={workspaceDetail}
          isShowInfo={isShowInfo}
          onClose={() => setShowInfo(false)}
        />}
      </div>
    </div>
    <Move
      isShowModal={stateModalMove.visible}
      handleCancel={() => toggleMove(null)}
      projectId={stateModalMove.projectId}
      handleAfterMove={handleAfterMove}
      workspaceId={workspaceId}
    />
    <Share
      isShowModal={stateModalShare.visible}
      handleCancel={toggleShare}
      queryAccess={stateModalShare.query}
      name={stateModalShare.name}
      owner={stateModalShare.owner}
      workspaceId={workspaceId}
    />
    <RenameDescription
      isShowModal={stateModalRename.visible}
      initialValue={stateModalRename.initialValue}
      handleClose={() => toggleRename(null)}
      handleSubmit={handleRename}
      placeholder={stateModalRename.placeholder}
      required
    />
  </WorkspaceContext.Provider>);
}

function mapStateToProps(store) {
  const { user } = store.auth;
  const { availableWorkspaces } = store.workspace;
  return { user, availableWorkspaces };
}

const mapDispatchToProps = {
  ...workspaceRedux.actions,
};

const ConnectedWorkspace = connect(mapStateToProps, mapDispatchToProps)(Workspace);

const useWorkspace = () => useContext(WorkspaceContext);

export { ConnectedWorkspace as Workspace, useWorkspace };
