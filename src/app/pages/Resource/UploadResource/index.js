import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { Tabs } from "antd";
import { useResource } from "@app/pages/Resource";

import AntModal from "@component/AntModal";
import ResourceDropzone from "@app/pages/Resource/UploadResource/ResourceDropzone";
import LinkYoutube from "@app/pages/Resource/UploadResource/LinkYoutube";

import { CONSTANT } from "@constant";
import { RESOURCE_CONFIG } from "@app/pages/Resource/resourceCommon";

import "./UploadResource.scss";


function UploadResource({ user, ...props }) {
  const { t } = useTranslation();
  const { resourceTypeActive, isShowUpload, setShowUpload } = useResource();
  
  const [videoType, setVideoType] = useState(CONSTANT.LINK);
  
  
  useEffect(() => {
    if (!isShowUpload) {
      setVideoType(CONSTANT.LINK);
    }
  }, [isShowUpload]);
  
  return <AntModal
    width={1058}
    title={t(RESOURCE_CONFIG[resourceTypeActive]?.title)}
    open={isShowUpload}
    onCancel={() => setShowUpload(false)}
    footerless
  >
    
    {resourceTypeActive === CONSTANT.VIDEO && <div className="resource__video-type">
      <Tabs
        activeKey={videoType}
        items={[
          { key: CONSTANT.LINK, label: t("LINK_YOUTUBE") },
          { key: CONSTANT.FILE, label: t("VIDEO_FILE") },
        ]}
        onChange={setVideoType}
      />
    </div>}
    
    {(videoType === CONSTANT.FILE || resourceTypeActive !== CONSTANT.VIDEO)
      ? <ResourceDropzone/>
      : <LinkYoutube/>}
  
  
  </AntModal>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(UploadResource);