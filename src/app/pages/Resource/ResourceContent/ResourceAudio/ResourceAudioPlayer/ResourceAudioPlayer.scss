.resource-audio-player {
  margin: -6px 0;

  .audio-player {

    .audio-player__controls {
      display: flex;
      border-radius: 8px;
      overflow: hidden;

      &.audio-player__controls-small {
        .play-button {
          height: 32px;
          width: 32px;
        }
      }

      .play-button {
        height: 40px;
        width: 40px;
        background: var(--primary-colours-blue-navy);
        border: none;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .progress-bar-container {
        color: var(--primary-colours-blue-navy);
        display: flex;
        gap: 8px;
        background: var(--primary-colours-blue-navy-light-1);
        flex: 1;
        padding: 0 8px;


        .progress-bar {
          align-self: center;
          flex: 1;
          cursor: pointer;
          height: 8px;
          -webkit-appearance: none;
          margin: 0;
        }

        .duration {
          align-self: center;
        }

        .volume-button {
          align-self: center;
          //width: 24px;
          //height: 24px;
          cursor: pointer;
        }
      }

      input[type=range] {
        --range: calc(var(--max) - var(--min));
        --ratio: calc((var(--value) - var(--min)) / var(--range));
        --sx: calc(var(--ratio) * 100%);
      }

      input[type=range]:focus {
        outline: none;
      }


      input[type=range]::-webkit-slider-thumb {
        width: var(--thumb-width);
        height: 8px;
        background: var(--primary-colours-blue-navy);
        -webkit-appearance: none;
      }

      input[type="range"]::-moz-range-thumb {
        width: var(--thumb-width);
        height: 8px;
        background: var(--primary-colours-blue-navy);
      }

      input[type=range]::-webkit-slider-runnable-track {
        height: 8px;
        background: #efefef;
        border: none;
        box-shadow: none;
      }

      input[type=range]::-webkit-slider-thumb:hover {
        background: var(--primary-colours-blue-navy);
      }

      input[type=range]:hover::-webkit-slider-runnable-track {
        background: #e5e5e5;
      }

      input[type=range]::-webkit-slider-thumb:active {
        background: var(--primary-colours-blue-navy);
      }

      input[type=range]:active::-webkit-slider-runnable-track {
        background: #f5f5f5;
      }

      input[type=range]::-webkit-slider-runnable-track {
        background: linear-gradient(var(--primary-colours-blue-navy), var(--primary-colours-blue-navy)) 0/var(--sx) 100% no-repeat, #FFF;
      }

      input[type=range]:hover::-webkit-slider-runnable-track {
        background: linear-gradient(var(--primary-colours-blue-navy), var(--primary-colours-blue-navy)) 0/var(--sx) 100% no-repeat, #FFF;
      }

      input[type=range]:active::-webkit-slider-runnable-track {
        background: linear-gradient(var(--primary-colours-blue-navy), var(--primary-colours-blue-navy)) 0/var(--sx) 100% no-repeat, #FFF;
      }


    }
  }
}