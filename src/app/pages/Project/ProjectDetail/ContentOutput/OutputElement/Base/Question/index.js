import React from "react";
import { Button } from "antd";

import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";

import ArrowUp from "@component/SvgIcons/ArrowUp";
import ArrowDown from "@component/SvgIcons/ArrowDown";
import PlusIcon from "@component/SvgIcons/PlusIcon";

import "./Question.scss";


function Question({ children, ...props }) {
  
  return <div className="question-list">
    {children}
  </div>;
}

function Item({ showMoveUp, showMoveDown, onMoveUp, onMoveDown, contentClassName, children, ...props }) {
  return <div className="question-item">
    <div className="question__move">
      {!!showMoveUp &&
        <AntButton
          size="compact"
          type={BUTTON.GHOST_WHITE}
          icon={<ArrowUp />} className="question__move-up"
          onClick={onMoveUp}
        />}
      {!!showMoveDown &&
        <AntButton
          size="compact"
          type={BUTTON.GHOST_WHITE}
          icon={<ArrowDown />} className="question__move-down"
          onClick={onMoveDown}
        />}
    </div>
    <div className={`question__content ${contentClassName}`}>
      {children}
    </div>
  </div>;
}

function Add({ onClick }) {
  return <Button
    ghost
    shape="circle"
    className="question__add"
    icon={<PlusIcon />}
    onClick={onClick}
  />;
}

Question.Item = Item;
Question.Add = Add;

export default Question;