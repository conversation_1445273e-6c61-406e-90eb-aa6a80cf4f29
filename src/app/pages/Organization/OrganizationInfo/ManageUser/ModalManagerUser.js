import React from "react";
import { AntForm } from "@component/AntForm";
import RULE from "@rule";
import { Input, Select } from "antd";
import { CONSTANT } from "@constant";
import AntModal from "@component/AntModal";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

function ModalManagerUser({ isShowModal, handleCancel, handleOk, titleOk = "Done", form, title, formId, isEdit, user }) {
  const { t } = useTranslation();

  const roleItems = [
    {
      label: t("ORGANIZATION_MANAGER"),
      value: CONSTANT.ADMIN,
    },
    {
      label: t("MEMBER"),
      value: CONSTANT.NORMAL,
    },
    {
      label: t("CURRICULUM_MANAGER"),
      value: CONSTANT.CONTRIBUTOR,
    },
  ];

  const optionRender = (options) => {
    switch (options.value) {
      case CONSTANT.ADMIN: return (<>
        <div className="font-semibold">{t("ORGANIZATION_MANAGER")}</div>
        <div className="font-normal">{t("ORGANIZATION_MANAGER_DESCRIPTION")}</div>
      </>)
      case CONSTANT.NORMAL: return (<>
        <div className="font-semibold">{t("MEMBER")}</div>
        <div className="font-normal">{t("ROLE_MEMBER_DESCRIPTION")}</div>
      </>)
      case CONSTANT.CONTRIBUTOR: return (<>
        <div className="font-semibold">{t("CURRICULUM_MANAGER")}</div>
        <div className="font-normal">{t("CURRICULUM_MANAGER_DESCRIPTION")}</div>
      </>)
    }
  }

  return (
    <div>
      <AntModal
        open={Boolean(isShowModal)}
        title={<span className="modal-add-new-user-organization__title">{title}</span>}
        onCancel={handleCancel}
        okText={titleOk}
        cancelText={t("CANCEL")}
        closeIcon={null}
        formId={formId}
        className="modal-add-new-user-organization"
      >
        <AntForm form={form} layout="vertical" size="large" id={formId} onFinish={handleOk}>
          <AntForm.Item name="email" label="Email" rules={[RULE.REQUIRED, RULE.EMAIL]}>
            <Input placeholder={t("ENTER_EMAIL")} disabled={isEdit} />
          </AntForm.Item>
          {isEdit && (
            <AntForm.Item name="role" label={t("ROLE")} rules={[RULE.REQUIRED]}>
              <Select
                placeholder={t("PLEASE_SELECT_ONE_ROLE")}
                options={roleItems}
                optionRender={optionRender}
                popupClassName="select-member-role-popup"
                virtual={false} />
            </AntForm.Item>
          )}
        </AntForm>
      </AntModal>
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(ModalManagerUser);
