import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import FolderProjectList from "@component/FolderProjectList";
import HomePageSection from "../HomePageSection";
import WorkspaceInfo from "@component/WorkspaceInfo";
import WorkspaceCreateNew from "@src/app/component/WorkspaceCreateNew";
import SelectWorkspace from "@component/SelectWorkspace";

import SELECT_WORKSPACE from "@src/asset/icon/selectWorkspace/select-workspace.svg";

import { LINK } from "@link";
import "./HomePageMyWorkspace.scss";

function HomePageMyWorkspace({ ...props }) {
  const { t } = useTranslation();
  const { dataSource, workspaces, workspaceActive } = props;
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterChangeStarred, handleAfterMove, setWorkspaceActive } = props

  const workspaceDetail = useMemo(() => {
    return workspaces.find((workspace) => workspace._id === workspaceActive);
  }, [workspaceActive, workspaces]);

  const title = useMemo(() => {
    if (!workspaces) {
      return null;
    }
    if (workspaces.length === 1) {
      return t("MY_WORKSPACE")
    }
    return <SelectWorkspace
      workspaces={workspaces}
      activeKey={workspaceActive}
      setActiveKey={setWorkspaceActive} />;
  }, [workspaceActive, setWorkspaceActive, workspaces]);

  const linkShowMore = useMemo(() => {
    if (!workspaces || dataSource?.length < 8) {
      return null;
    }
    if (workspaces.length === 1 || workspaceActive === workspaces[0]._id) {
      return LINK.MY_WORKSPACE;
    }
    return LINK.ORGANIZATION_WORKSPACE;
  }, [workspaces, workspaceActive, dataSource]);

  return (<HomePageSection
    title={title}
    icon={SELECT_WORKSPACE}
    extra={<WorkspaceCreateNew workspaceId={workspaceActive} />}
  >
    <div className="homepage-workspace-body">
      <div className="col-span-1">
        <WorkspaceInfo workspaceDetail={workspaceDetail} />
      </div>
      <div className="folder-project-list-wraper">
        <FolderProjectList
          dataSource={dataSource.slice(0, 8)}
          isWorkspace
          linkShowMore={linkShowMore}
          //showOwner
          allowActions
          handleAfterCopy={handleAfterCopy}
          handleAfterRename={handleAfterRename}
          handleAfterDelete={handleAfterDelete}
          handleAfterChangeStarred={handleAfterChangeStarred}
          handleAfterMove={handleAfterMove}
        />
      </div>
    </div>
  </HomePageSection>);
}

export default HomePageMyWorkspace;
