import React, { useEffect, useMemo, useState } from "react";
import { Checkbox, Input } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";

import { updateInfoUser } from "@services/Auth";

import CLICKEE_LIGHT from "@src/asset/logo/clickee-light.svg";

import * as auth from "@src/ducks/auth.duck";

import "./SuggestiveQuestion.scss";


const SUGGEST_QUESTIONS = {
  TEACHER_FOR_GRADES_1_2_3: { value: "TEACHER_FOR_GRADES_1_2_3", lang: "TEACHER_FOR_GRADES_1_2_3" },
  LECTURER_AT_UNIVERSITY: { value: "LECTURER_AT_UNIVERSITY", lang: "LECTURER_AT_UNIVERSITY" },
  LANGUAGE_CENTER: { value: "LANGUAGE_CENTER", lang: "LANGUAGE_CENTER" },
  IELTS_CENTER: { value: "IELTS_CENTER", lang: "IELTS_CENTER" },
  INDEPENDENT_TEACHER: { value: "INDEPENDENT_TEACHER", lang: "INDEPENDENT_TEACHER" },
  ENGLISH_CENTER_MANAGER: { value: "ENGLISH_CENTER_MANAGER", lang: "ENGLISH_CENTER_MANAGER" },
  OTHER: { value: "OTHER", lang: "OTHER" },
};

function SuggestiveQuestion({ user, ...props }) {
  const { t } = useTranslation();
  
  const [questionSelected, setQuestionSelected] = useState([]);
  const [otherOption, setOtherOption] = useState("");
  
  function handleChangeQuestionSelected(question) {
    if (questionSelected.includes(question)) {
      setQuestionSelected(prevState => prevState.filter(state => state !== question));
    } else {
      setQuestionSelected(prevState => [...prevState, question]);
    }
  }
  
  const isShowOtherOption = useMemo(() => {
    return questionSelected.includes(SUGGEST_QUESTIONS.OTHER.value);
  }, [questionSelected]);
  
  const isDisabledSubmit = useMemo(() => {
    if (!questionSelected.length) return true;
    return questionSelected.includes(SUGGEST_QUESTIONS.OTHER.value) && !otherOption?.trim();
    
  }, [questionSelected, otherOption]);
  
  useEffect(() => {
    if (!isShowOtherOption) {
      setOtherOption("");
    }
  }, [isShowOtherOption]);
  
  
  async function submitSuggestQuestion() {
    const apiRequest = { persona: questionSelected };
    if (questionSelected.includes(SUGGEST_QUESTIONS.OTHER.value)) {
      apiRequest.persona.push(otherOption);
    }
    
    const apiResponse = await updateInfoUser(apiRequest);
    if (apiResponse) {
      props.userLoaded({ ...user, persona: apiResponse.persona });
    }
  }
  
  return <div className="suggest-question-container">
    <div className="suggest-question-content">
      {/* <div className="suggest-question__logo">
        <img className="logo-header" src={CLICKEE_LIGHT} alt="" />
      </div> */}
      <div className="suggest-question__question">
        {t("TO_HELP_CLICKEE_SERVE_YOU_BETTER_PLEASE_LET_US_KNOW_WHO_YOU_ARE")} <span
        className="font-normal">({t("MULTIPLE_SELECTIONS_POSSIBLE")})</span>
      </div>
      <div className="suggest-question__answer">
        {Object.values(SUGGEST_QUESTIONS).map(question => {
          return <Checkbox
            key={question.value}
            value={question.value}
            checked={questionSelected.includes(question.value)}
            onChange={() => handleChangeQuestionSelected(question.value)}
          >
            {t(question.lang)}
          </Checkbox>;
        })}
      </div>
      {isShowOtherOption && <>
        <div className="suggest-question__question">
          {t("COULD_YOU_TELL_ME_WHAT_YOUR_OTHER_OPTION_IS")}
        </div>
        <div className="suggest-question__answer">
          <Input
            size="large"
            placeholder={t("DETAILS_ABOUT_THE_OTHER_OPTION")}
            value={otherOption}
            onChange={(e) => setOtherOption(e.target.value)}
          />
        </div>
      </>}
      <div className="suggest-question__submit">
        <AntButton
          size="large"
          type={BUTTON.LIGHT_NAVY}
          disabled={isDisabledSubmit}
          onClick={submitSuggestQuestion}
        >
          {t("NEXT")}
        </AntButton>
      </div>
    </div>
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = { ...auth.actions };

export default connect(mapStateToProps, mapDispatchToProps)(SuggestiveQuestion);