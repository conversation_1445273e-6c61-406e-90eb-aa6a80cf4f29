import { API } from "@api";
import { createBase, deleteBase, getDetailBase, updateBase } from "../Base";

export function createDataset(data, toastError = false) {
  return createBase(API.DATASET, data, [], false, toastError);
}

export function getDetailDataset(datasetId) {
  return getDetailBase(API.DATASET_ID, datasetId, ["instructionId.outputTypeId"], true);
}

export function updateDetailDataset(data, toastError = false) {
  return updateBase(API.DATASET_ID, data, [], false, toastError);
}

export function deleteDataset(id, toastError = false) {
  return deleteBase(API.DATASET_ID, id, false, toastError);
}
