import { API } from "@api";
import { createBase, deleteBase, getAllBase, getAllPaginationBase, getDetailBase, updateBase } from "@services/Base";

// <PERSON>ến dịch email
export function getPaginationCampaigns(paging, query, searchFields = ["name"], populateOpts = ["targetGroups", "templateId"]) {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.EMAIL_CAMPAIGN, paging, query, searchFields, populateOpts);
}

export function getCampaignDetail(id, populateOpts = ["targetGroups", "templateId"]) {
  return getDetailBase(API.EMAIL_CAMPAIGN_ID, id, populateOpts);
}

export function createCampaign(data) {
  return createBase(API.EMAIL_CAMPAIGN, data);
}

export function updateCampaign(data) {
  return updateBase(API.EMAIL_CAMPAIGN_ID, data);
}

export function deleteCampaign(id) {
  return deleteBase(API.EMAIL_CAMPAIGN_ID, id);
}

export function updateCampaignStatus(id, status) {
  return updateBase(API.EMAIL_CAMPAIGN_STATUS, { _id: id, status });
}

export function getCampaignStatistics(id) {
  return getDetailBase(API.EMAIL_CAMPAIGN_STATISTICS, id);
}

// Nhóm người dùng
export function getPaginationGroups(paging, query, searchFields = ["name"], populateOpts = []) {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.EMAIL_GROUP, paging, query, searchFields, populateOpts);
}

export function getGroupDetail(id, populateOpts = []) {
  return getDetailBase(API.EMAIL_GROUP_ID, id, populateOpts);
}

export function createGroup(data) {
  return createBase(API.EMAIL_GROUP, data);
}

export function updateGroup(data) {
  return updateBase(API.EMAIL_GROUP_ID, data, [], true, false, { hideNoti: true });
}

export function deleteGroup(id) {
  return deleteBase(API.EMAIL_GROUP_ID, id);
}

export function refreshGroup(id) {
  return updateBase(API.EMAIL_GROUP_REFRESH, { _id: id });
}

// Mẫu email
export function getPaginationTemplates(paging, query, searchFields = ["name", "subject"], populateOpts = []) {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.EMAIL_TEMPLATE, paging, query, searchFields, populateOpts);
}

export function getTemplateDetail(id, populateOpts = []) {
  return getDetailBase(API.EMAIL_TEMPLATE_ID, id, populateOpts);
}

export function createTemplate(data) {
  return createBase(API.EMAIL_TEMPLATE, data);
}

export function updateTemplate(data) {
  return updateBase(API.EMAIL_TEMPLATE_ID, data);
}

export function deleteTemplate(id) {
  return deleteBase(API.EMAIL_TEMPLATE_ID, id);
}

// Thống kê
export function getEmailStatistics(query) {
  return getAllBase(API.EMAIL_STATISTICS, query);
}
