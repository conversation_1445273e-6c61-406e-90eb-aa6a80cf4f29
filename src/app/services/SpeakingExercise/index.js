import { API } from "@api";
import { createBase, deleteBase, getAllPaginationBase, getDetailBase, updateBase } from "@services/Base";
import axios from "axios";

export function getPaginationExercise(paging, query) {
  return getAllPaginationBase(API.SPEAKING_EXERCISE, paging, query, ["title"]);
}

export function getExerciseDetail(id) {
  return getDetailBase(API.SPEAKING_EXERCISE_ID, id, []);
}

export function updateExercise(data) {
  return updateBase(API.SPEAKING_UPDATE_EXERCISE, data);
}

export function textToAudio(data) {
  return axios.post(API.SPEAKING_CREATE_AUDIO, (data))
              .then(response => {
                if (response.status === 200) return response.data;
                return null;
              })
              .catch((err) => null);
}

export function createQuestionAudio(data) {
  return axios.post(API.SPEAKING_CREATE_QUESTION_AUDIO, (data))
              .then(response => {
                if (response.status === 200) return response.data;
                return null;
              })
              .catch((err) => null);
}

export function deleteExercise(id) {
  return deleteBase(API.SPEAKING_DELETE_EXERCISE, id);
}

export function createExercise(data) {
  return createBase(API.SPEAKING_CREATE_EXERCISE, data);
}

export function uploadExerciseAudio(file, config = {}) {
  config.headers = { "content-type": "multipart/form-data" };
  const formData = new FormData();

  formData.append("fileType", "file");
  formData.append("file", file);
  return axios.post(API.SPEAKING_UPLOAD_AUDIO, formData, config)
              .then(response => {
                if (response.status === 200) return response.data;
                return null;
              })
              .catch(err => null);
}

export function createHint(data) {
  return axios.post(API.SPEAKING_CREATE_HINT, data)
              .then(response => {
                if (response.status === 200) return response.data;
                return null;
              })
              .catch(err => null);
}
